import { Checkbox, styled } from "@mui/material";
import styles from "./custom-checkbox.module.css";

interface CustomCheckboxProps {
  checked: boolean;
  formName?: string;
  onChange?: (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean
  ) => void;
  label?: string;
}

const CheckmarkBox = styled("span")`
  display: inline-block;
  box-sizing: content-box;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  width: 16px;
  height: 16px;
  border: 1px solid #70b57d;
  background-color: white;
  transition: all 0.2s ease;
`;

const CheckedCheckmarkBox = styled(CheckmarkBox)`
  background-color: white !important;
  width: 16px;
  height: 16px;

  &::before {
    content: "";
    background-image: url("/assets/checkedIcon.svg");
    display: block;
    width: 16px;
    height: 16px;
  }
`;

export const CustomCheckbox = ({
  label,
  checked,
  onChange,
  formName,
}: CustomCheckboxProps) => {
  return (
    <div className={styles.mainCheckboxContainer}>
      <Checkbox
        onChange={onChange}
        disableRipple
        checked={checked}
        checkedIcon={<CheckedCheckmarkBox />}
        name={formName}
        icon={<CheckmarkBox />}
        sx={{
          padding: 0,
        }}
      />
      {label}
    </div>
  );
};
