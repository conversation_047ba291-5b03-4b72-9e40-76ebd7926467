import { z } from "zod";
import * as Lists from "../../shared/lists";

const string = z.string().nullable();
const number = z.number().nullable();

export const ExtractMetadata = z.object({
    origin: z.object({
        country: string,
        city: string,
        zipCode: string,
        address: string,
    }),

    destination: z.object({
        country: string,
        city: string,
        zipCode: string,
        address: string,
    }),

    incoterms: z.array(z.enum(Lists.incoterms)).nullable(),
    serviceType: z.enum(Lists.services).nullable(),
});

export const ExtractDigits = z.object({
    totalUnits: number,
    totalWeight: number,
    totalVolume: number,

    packages: z
        .array(z.object({
            units: number,
            length: number,
            width: number,
            height: number,
            weight: number,

            type: z
                .enum(["box", "crate", "pallet"])
                .nullable(),

            isStackable: z.boolean(),
        }))
        .nullable(),
});

export const ExtractServices = z.object({
    descriptionOfGoods: string,
    hsCodes: z.array(z.coerce.number()).nullable(),
    totalCostOfGoods: number,
    currency: string,
    additionalServices: z.array(z.enum(Lists.additionalServices)).nullable(),
    dangerousGoods: z.array(z.enum(Lists.dangerousGoods)).nullable(),
});
