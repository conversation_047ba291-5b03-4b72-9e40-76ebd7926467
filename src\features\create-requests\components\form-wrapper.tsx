import { forwardRef, memo, useEffect, useImperativeHandle } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as Lists from "../../../shared/lists";
import { CustomAlert } from "../../../components";
import { FormData, ShippingInfo } from "../types/shippingTypes";
import { defaultShippingInfo } from "../utils/consts";
import { generateResultText } from "../utils/generateResultText";
import { ShippingSchemaWithValidation } from "../utils/validationSchema";
import { useSaveRequest } from "../data/useSaveRequest";

import {
  TResultText,
  AddressesInfo,
  ShipmentItemsForm,
  AdditionalDetails,
} from ".";

type Props = {
  data: ShippingInfo | undefined;
  setResultText: (text: TResultText) => void;
};

const options = {
  incoterms: [...Lists.incoterms],
  service: [...Lists.services],
  additional_services: [...Lists.additionalServices],
  package_type: [...Lists.packageTypes],
  dangerous_goods: [...Lists.dangerousGoods],
}

export const FormWrapper = memo(forwardRef(({ data, setResultText }: Props, ref) => {
  const methods = useForm<FormData>({
    resolver: zodResolver(ShippingSchemaWithValidation),
    defaultValues: defaultShippingInfo,
    mode: "onSubmit",
    reValidateMode: "onSubmit",
  });

  // @ts-expect-error currently not used
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { setValue, handleSubmit, watch, setError, trigger } = methods;
  const { saveRequest, success, error: savingError } = useSaveRequest({
    handleFieldErrors: setError,
  });

  watch((data) => {
    const newText = generateResultText(data);
    setResultText(newText);
  });

  useImperativeHandle(ref, () => ({
    submitForm: async () => {
      console.log("submitForm");

      if (data) {
        await saveRequest(data);
      }
    },
    reset: () => {
      methods.reset();
    },
  }));

  useEffect(() => {
    if (!data) return;

    Object.keys(data).forEach((key) => {
      setValue(key as keyof ShippingInfo, data[key as keyof ShippingInfo]);
    });
    trigger()
  }, [data, setValue, trigger]);

  return (
    <>
      <CustomAlert message={savingError} severity="error" />
      <CustomAlert message={success} severity="success" />
      <FormProvider {...methods}>
        <form
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
            }
          }}
        >
          <AddressesInfo deliveryOptions={options} />

          <ShipmentItemsForm packageOptions={options.package_type} />
          <AdditionalDetails
            additionalServices={options.additional_services}
            dangerousGoods={options.dangerous_goods}
          />
        </form>
      </FormProvider>
    </>
  );
}));
