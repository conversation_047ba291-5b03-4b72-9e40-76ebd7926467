import { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  Checkbox,
  Collapse,
  CircularProgress,
} from '@mui/material';
import { TextFieldWithLabelWithMultipleValues } from '../../../new-main-page-2-components';
import FilterListIcon from '@mui/icons-material/FilterList';
import DeleteIcon from '@mui/icons-material/Delete';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import { Icon } from '../../../features/create-requests/assets';
import { CustomAlert } from '../../../components';
import { CopyButton } from '../../../features/create-requests/components';
import styles from '../../../new-main-page.module.css';

// Mock data types
type TariffRate = {
  id: string;
  transport: 'AIR' | 'SEA' | 'RAIL' | 'ROAD';
  agent: string | null;
  direction: {
    from: string | null;
    to: string | null;
  };
  carrier: string | null;
  transitTime: string;
  total: {
    value: number;
    currency: string;
  };
  margin: {
    value: number;
    type: 'fixed' | 'percent';
  };
  sellingRate: {
    value: number;
    currency: string;
  };
  profit: {
    value: number;
    currency: string;
  };
  isStarred: boolean;
  isExpanded: boolean;
  commonCharges: CommonCharge[];
  localCharges: LocalCharge[];
};

type CommonCharge = {
  id: string;
  type: string;
  unit: string;
  rate: number;
  total: number;
  currency: string;
};

type LocalCharge = {
  id: string;
  type: string;
  unit: string;
  rate: number;
  total: number;
  currency: string;
  margin: number;
  marginUnit: 'fixed' | 'percent';
  marginRate: number;
  marginTotal: number;
  profit: number;
  useInCalculation: boolean;
};

export default function TariffsPage() {
  // Mock request data
  const requestData = {
    ordinal: '001',
    marshroute: 'Belgrade - San Francisco',
    quantity: 10,
    weight: 1500,
    volume: 25.5,
    chargeableWeight: 1600
  };

  // State for text input
  const [text, setText] = useState('');
  const [textError, setTextError] = useState('');
  const [isPendingSend, setIsPendingSend] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [resultTitle, setResultTitle] = useState('');
  const [resultContent, setResultContent] = useState('');

  // State for filters
  const [transportFilter, setTransportFilter] = useState('');
  const [agentFilter, setAgentFilter] = useState('');

  // Mock tariff rates data
  const [tariffRates, setTariffRates] = useState<TariffRate[]>([
    {
      id: '1',
      transport: 'AIR',
      agent: 'Company A',
      direction: { from: 'Belgrade', to: 'San Francisco' },
      carrier: 'Carrier 1',
      transitTime: '8 days',
      total: { value: 2500, currency: 'USD' },
      margin: { value: 15, type: 'percent' },
      sellingRate: { value: 2875, currency: 'USD' },
      profit: { value: 375, currency: 'USD' },
      isStarred: true,
      isExpanded: false,
      commonCharges: [
        { id: 'c1', type: 'Fuel surcharge', unit: 'kg', rate: 0.5, total: 750, currency: 'USD' },
        { id: 'c2', type: 'Security fee', unit: 'shipment', rate: 50, total: 50, currency: 'USD' }
      ],
      localCharges: [
        {
          id: 'l1',
          type: 'Terminal handling',
          unit: 'shipment',
          rate: 100,
          total: 100,
          currency: 'USD',
          margin: 10,
          marginUnit: 'percent',
          marginRate: 10,
          marginTotal: 10,
          profit: 10,
          useInCalculation: true
        }
      ]
    },
    {
      id: '2',
      transport: 'SEA',
      agent: 'Company B',
      direction: { from: 'Belgrade', to: 'San Francisco' },
      carrier: 'Carrier 2',
      transitTime: '25 days',
      total: { value: 1200, currency: 'USD' },
      margin: { value: 200, type: 'fixed' },
      sellingRate: { value: 1400, currency: 'USD' },
      profit: { value: 200, currency: 'USD' },
      isStarred: false,
      isExpanded: false,
      commonCharges: [],
      localCharges: []
    }
  ]);

  // Placeholder functions - no functionality implemented
  const send = () => {
    // No functionality
  };

  const paste = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setText(prev => prev + (prev ? '\n' : '') + clipboardText);
    } catch (err) {
      console.error('Failed to read clipboard contents: ', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    // No functionality
  };

  const removeFile = (index: number) => {
    // No functionality
  };

  const getFileExtension = (filename: string) => {
    return filename.split('.').pop()?.toUpperCase() || '';
  };

  const toggleStar = (id: string) => {
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === id ? { ...rate, isStarred: !rate.isStarred } : rate
      )
    );
  };

  const toggleExpand = (id: string) => {
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === id ? { ...rate, isExpanded: !rate.isExpanded } : rate
      )
    );
  };

  const deleteRate = (id: string) => {
    setTariffRates(rates => rates.filter(rate => rate.id !== id));
  };

  const addCommonCharge = (rateId: string) => {
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            commonCharges: [
              ...rate.commonCharges,
              {
                id: `c${Date.now()}`,
                type: '',
                unit: '',
                rate: 0,
                total: 0,
                currency: 'USD'
              }
            ]
          }
          : rate
      )
    );
  };

  const addLocalCharge = (rateId: string) => {
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            localCharges: [
              ...rate.localCharges,
              {
                id: `l${Date.now()}`,
                type: '',
                unit: '',
                rate: 0,
                total: 0,
                currency: 'USD',
                margin: 0,
                marginUnit: 'fixed',
                marginRate: 0,
                marginTotal: 0,
                profit: 0,
                useInCalculation: true
              }
            ]
          }
          : rate
      )
    );
  };

  return (
    <Box sx={{ padding: '20px', backgroundColor: '#FFFFFF', minHeight: '100vh' }}>
      {/* Request Data Line */}
      <Box sx={{
        backgroundColor: '#FFFFFF',
        padding: '16px',
        borderRadius: '8px',
        marginBottom: '20px',
        fontFamily: "'YS Text', sans-serif",
        position: 'relative'
      }}>
        <Box sx={{
          display: 'flex',
          gap: '20px',
          alignItems: 'flex-end'
        }}>
          {/* Ordinal */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              request №
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.ordinal}
            </Box>
          </Box>

          {/* Route */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Itinerary
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.marshroute}
            </Box>
          </Box>

          {/* Quantity */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              piece
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.quantity}
            </Box>
          </Box>

          {/* Weight */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              weight
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.weight} kg
            </Box>
          </Box>

          {/* Volume */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Volume
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.volume} m³
            </Box>
          </Box>

          {/* Chargeable Weight */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Chargeable Weight
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.chargeableWeight} kg
            </Box>
          </Box>
        </Box>

        {/* Horizontal line under the Request Data Line */}
        <Box sx={{
          marginTop: '8px',
          height: '1px',
          backgroundColor: '#C6C5CA',
          alignSelf: 'stretch'
        }} />
      </Box>

      {/* Text Blocks Section */}
      <div className={styles.textContainer}>
        <div className={styles.label}>
          Paste here the text of request for cargo transportation
        </div>

        <button
          onClick={send}
          disabled={isPendingSend || (!text.trim().length && uploadedFiles.length === 0)}
          className={styles.sendButton}
        >
          {isPendingSend ? (
            <CircularProgress
              size={16}
              style={{ position: "absolute", color: "white" }}
            />
          ) : (
            <Icon.Send />
          )}
        </button>

        <div className={styles.textInputsContainer}>
          <CustomAlert message={textError} severity="error" />
          <div className={styles.textIntoInputContainer}>
            <div className={styles.textFieldContainer}>
              <textarea
                className={styles.textField}
                value={text}
                onChange={(e) => setText(e.target.value)}
              />

              {/* File upload container in bottom right corner */}
              <div className={styles.fileUploadContainer}>
                {/* File gallery */}
                <div className={styles.fileGallery}>
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className={styles.fileItem}>
                      <button
                        className={styles.fileRemoveButton}
                        onClick={() => removeFile(index)}
                        title="Remove file"
                      >
                        ×
                      </button>
                      <div className={styles.fileExtension}>
                        {getFileExtension(file.name)}
                      </div>
                      <div className={styles.fileName} title={file.name}>
                        {file.name}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Upload button */}
                <input
                  type="file"
                  multiple
                  className={styles.hiddenFileInput}
                  onChange={handleFileUpload}
                  // accept="*/*"
                  accept=".txt, .docx, .xlsx, .pdf"
                  id="file-upload-input"
                  disabled={uploadedFiles.length >= 3}
                />
                <label
                  htmlFor="file-upload-input"
                  className={styles.fileUploadButton}
                  title={uploadedFiles.length >= 3 ? "Maximum 3 files allowed" : "Upload files"}
                  style={{
                    opacity: uploadedFiles.length >= 3 ? 0.5 : 1,
                    cursor: uploadedFiles.length >= 3 ? 'not-allowed' : 'pointer'
                  }}
                >
                  <Icon.Upload />
                </label>
              </div>
            </div>

            <Button
              disableElevation
              onClick={paste}
              startIcon={<Icon.Paste />}
              size="small"
              variant="text"
              sx={{
                fontSize: "12px",
                textTransform: "lowercase",
                cursor: "pointer",
                fontWeight: "400",
                lineHeight: '16px',
                marginTop: "5px",
              }}
            >
              paste
            </Button>
          </div>

          <div className={styles.resultTextContainer}>
            <Select
              value="wip"
              displayEmpty
              size="small"
              sx={{
                marginBottom: '12px',
                minWidth: '120px',
                fontSize: '12px',
                fontFamily: "'YS Text', sans-serif"
              }}
            >
              <MenuItem value="wip">wip</MenuItem>
            </Select>

            <div className={styles.titleResult}>
              <div style={{ flexShrink: 2, overflow: "auto" }}>{resultTitle}</div>
              <CopyButton text={resultTitle} disabled={!resultTitle} />
            </div>

            <div className={styles.fullTextResultContainer} style={{ height: 'calc(135px - 40px)' }}>
              <div className={styles.fullTextResult}>{resultContent}</div>
              <div className={styles.actionBtns}>
                <CopyButton text={resultContent} disabled={!resultContent} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <Box sx={{
        backgroundColor: '#FFFFFF',
        borderRadius: '8px 8px 0px 0px',
        overflow: 'hidden'
      }}>
        {/* Table Header */}
        <Box sx={{
          backgroundColor: '#70B57D',
          height: '66px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 12px',
          borderRadius: '8px 8px 0px 0px'
        }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%'
          }}>
            {/* Transport column with filter */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px', width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Transport
              </Box>
              <IconButton size="small" sx={{ color: '#FFFFFF' }}>
                <FilterListIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Box>

            {/* Agent column with filter */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px', width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Agent
              </Box>
              <IconButton size="small" sx={{ color: '#FFFFFF' }}>
                <FilterListIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Box>

            {/* Direction column */}
            <Box sx={{ width: '180px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Direction
              </Box>
            </Box>

            {/* Carrier column */}
            <Box sx={{ width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Carrier
              </Box>
            </Box>

            {/* Transit time column */}
            <Box sx={{ width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Transit time
              </Box>
            </Box>

            {/* Total column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Total
              </Box>
            </Box>

            {/* Margin column */}
            <Box sx={{ width: '120px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Margin
              </Box>
            </Box>

            {/* Selling rate column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Selling rate
              </Box>
            </Box>

            {/* Profit column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Profit
              </Box>
            </Box>

            {/* Actions column */}
            <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', justifyContent: 'flex-end' }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                {/* Actions */}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* Table Body */}
        <Box>
          {tariffRates.map((rate, index) => (
            <Box key={rate.id}>
              {/* Main row */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                padding: '0 12px',
                height: '64px',
                justifyContent: 'space-between',
                borderBottom: index < tariffRates.length - 1 ? '1px solid #C6C5CA' : 'none'
              }}>
                {/* Transport */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.transport}
                  </Box>
                </Box>

                {/* Agent */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.agent || '-'}
                  </Box>
                </Box>

                {/* Direction */}
                <Box sx={{ width: '180px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.direction.from && rate.direction.to
                      ? `${rate.direction.from} - ${rate.direction.to}`
                      : '-'}
                  </Box>
                </Box>

                {/* Carrier */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.carrier || '-'}
                  </Box>
                </Box>

                {/* Transit time */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.transitTime}
                  </Box>
                </Box>

                {/* Total */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.total.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.total.value.toLocaleString()}
                  </Box>
                </Box>

                {/* Margin */}
                <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <TextField
                    size="small"
                    value={rate.margin.value}
                    sx={{
                      width: '60px',
                      '& .MuiOutlinedInput-root': {
                        fontSize: '12px',
                        fontFamily: "'YS Text', sans-serif"
                      }
                    }}
                  />
                  <FormControl size="small" sx={{ minWidth: '40px' }}>
                    <Select
                      value={rate.margin.type}
                      sx={{
                        fontSize: '12px',
                        fontFamily: "'YS Text', sans-serif",
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #C6C5CA'
                        }
                      }}
                    >
                      <MenuItem value="fixed">Fixed</MenuItem>
                      <MenuItem value="percent">%</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                {/* Selling rate */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.sellingRate.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.sellingRate.value.toLocaleString()}
                  </Box>
                </Box>

                {/* Profit */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.profit.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.profit.value.toLocaleString()}
                  </Box>
                </Box>

                {/* Actions */}
                <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: '8px' }}>
                  <IconButton
                    size="small"
                    onClick={() => deleteRate(rate.id)}
                    sx={{ color: '#1B1D1F' }}
                  >
                    <DeleteIcon />
                  </IconButton>

                  <IconButton
                    size="small"
                    onClick={() => toggleStar(rate.id)}
                    sx={{ color: rate.isStarred ? '#70B57D' : '#1B1D1F' }}
                  >
                    {rate.isStarred ? <StarIcon /> : <StarBorderIcon />}
                  </IconButton>

                  <IconButton
                    size="small"
                    onClick={() => toggleExpand(rate.id)}
                    sx={{ color: '#1A1A1A', transform: rate.isExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                </Box>
              </Box>

              {/* Expanded content */}
              <Collapse in={rate.isExpanded}>
                <Box sx={{ padding: '16px', backgroundColor: '#F9F9F9' }}>
                  <Box sx={{ display: 'flex', gap: '20px' }}>
                    {/* Common charges */}
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{
                        fontSize: '14px',
                        fontWeight: 700,
                        color: '#1A1A1A',
                        fontFamily: "'YS Text', sans-serif",
                        marginBottom: '12px'
                      }}>
                        Common Charges
                      </Box>

                      {rate.commonCharges.map((charge) => (
                        <Box key={charge.id} sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          marginBottom: '8px'
                        }}>
                          <TextField
                            size="small"
                            placeholder="Type"
                            value={charge.type}
                            sx={{ width: '120px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Unit"
                            value={charge.unit}
                            sx={{ width: '80px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Rate"
                            value={charge.rate}
                            sx={{ width: '80px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Total"
                            value={charge.total}
                            sx={{ width: '80px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Currency"
                            value={charge.currency}
                            sx={{ width: '80px' }}
                          />
                          <IconButton size="small" sx={{ color: '#EB4E3D' }}>
                            <CloseIcon />
                          </IconButton>
                        </Box>
                      ))}

                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => addCommonCharge(rate.id)}
                        sx={{
                          fontSize: '12px',
                          textTransform: 'none',
                          color: '#70B57D'
                        }}
                      >
                        Add charge
                      </Button>
                    </Box>

                    {/* Local charges */}
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{
                        fontSize: '14px',
                        fontWeight: 700,
                        color: '#1A1A1A',
                        fontFamily: "'YS Text', sans-serif",
                        marginBottom: '12px'
                      }}>
                        Local Charges
                      </Box>

                      {rate.localCharges.map((charge) => (
                        <Box key={charge.id} sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          marginBottom: '8px'
                        }}>
                          <TextField
                            size="small"
                            placeholder="Type"
                            value={charge.type}
                            sx={{ width: '100px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Unit"
                            value={charge.unit}
                            sx={{ width: '60px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Rate"
                            value={charge.rate}
                            sx={{ width: '60px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Total"
                            value={charge.total}
                            sx={{ width: '60px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Currency"
                            value={charge.currency}
                            sx={{ width: '60px' }}
                          />
                          <TextField
                            size="small"
                            placeholder="Margin"
                            value={charge.margin}
                            sx={{ width: '60px' }}
                          />
                          <FormControl size="small" sx={{ width: '60px' }}>
                            <Select value={charge.marginUnit}>
                              <MenuItem value="fixed">Fixed</MenuItem>
                              <MenuItem value="percent">%</MenuItem>
                            </Select>
                          </FormControl>
                          <TextField
                            size="small"
                            placeholder="Profit"
                            value={charge.profit}
                            sx={{ width: '60px' }}
                          />
                          <Checkbox
                            checked={charge.useInCalculation}
                            size="small"
                          />
                          <IconButton size="small" sx={{ color: '#EB4E3D' }}>
                            <CloseIcon />
                          </IconButton>
                        </Box>
                      ))}

                      {/* Local charges total row */}
                      {rate.localCharges.length > 0 && (
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          marginBottom: '8px',
                          fontWeight: 700,
                          backgroundColor: '#EFEFEF',
                          padding: '8px',
                          borderRadius: '4px'
                        }}>
                          <Box sx={{ width: '100px' }}>Total</Box>
                          <Box sx={{ width: '60px' }}>-</Box>
                          <Box sx={{ width: '60px' }}>-</Box>
                          <Box sx={{ width: '60px' }}>
                            {rate.localCharges.reduce((sum, charge) => sum + charge.total, 0)}
                          </Box>
                          <Box sx={{ width: '60px' }}>-</Box>
                          <Box sx={{ width: '60px' }}>-</Box>
                          <Box sx={{ width: '60px' }}>-</Box>
                          <Box sx={{ width: '60px' }}>
                            {rate.localCharges.reduce((sum, charge) => sum + charge.profit, 0)}
                          </Box>
                        </Box>
                      )}

                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => addLocalCharge(rate.id)}
                        sx={{
                          fontSize: '12px',
                          textTransform: 'none',
                          color: '#70B57D'
                        }}
                      >
                        + Add charge
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </Collapse>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}