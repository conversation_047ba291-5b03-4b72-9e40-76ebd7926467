.mainCheckboxContainer {
  display: flex;
  gap: 8px;
  font-weight: 400;
  font-size: 12px;
  align-items: center;
}


.addressesInfoContainer {
  display: flex;
  justify-content: center;
  gap: 16px;

  width: 100%;

  flex-shrink: 1;
  flex-grow: 1;
}


.addressInfoContainer {
  border: 1px solid #C6C5CA;
  flex-basis: 392px;
  border-radius: 10px;

  padding: 16px 26px;

  display: flex;
  flex-direction: column;
  justify-content: start;
  flex-shrink: 1;
  flex-grow: 1;
  align-items: flex-start;
  gap: 16px;
}

.firstLineContainer {
  display: flex;
  gap: 16px;
  flex-shrink: 1;
  flex-grow: 1;
  width: 100%;
  align-items: stretch;
}

.labelContainer {
  display: flex;
  gap: 16px;
  align-items: center;
  font-weight: 700;
}

.packageInputsContainer {
  display: flex;
  flex-shrink: 1;
  width: 100%;
  align-items: start;

  gap: 16px;
}

.packageInputsContainer>div {
  flex-shrink: 1;
}

.packageAlignContainer {
  height: 52px;
  display: flex;
  align-items: center;
}