import type { Config } from "../../config";
import type { LlmService } from "..";

import { z } from "zod";
import path from "path";
import OpenAI from "openai";
import mammoth from "mammoth";
import { zodResponseFormat } from "openai/helpers/zod.mjs";
import { Injectable } from "../../utils";
import { getXlsxFileContent } from "./xlsx";

type PreparedFile =
    | { type: "text", content: string }
    | { type: "file", name: string, content: string };

export class OpenAiService extends Injectable implements LlmService {
    private readonly config!: Config;

    private openai!: OpenAI;

    override async init1() {
        const apiKey = this.config.getWithValidation(
            "OPENAI_API_KEY",
            z.string().nonempty(),
        );

        this.openai = new OpenAI({ apiKey });
    }

    async chatCompletion<T>(data: {
        model?: string;
        messages: OpenAI.ChatCompletionMessageParam[];
        responseFormat: {
            schema: z.ZodSchema<T>;
            name: string;
        }
    }) {
        const dto = {
            model: data.model ?? "gpt-4o-2024-08-06", // gpt-4o-mini?
            messages: data.messages,
            response_format: zodResponseFormat(
                data.responseFormat.schema,
                data.responseFormat.name,
            ),
        };

        const chatCompletion = await this.openai.beta.chat.completions.parse(dto);

        if (!chatCompletion.choices[0]) {
            throw new Error("No choices returned from OpenAI");
        }

        return chatCompletion.choices[0]!.message.parsed!;
    }

    async uploadFile(file: { buffer: Buffer, name: string }) {
        const result = await this.openai.files.create({
            file: await OpenAI.toFile(file.buffer, file.name),
            purpose: "user_data",
        });

        return {
            id: result.id,
        };
    }

    protected async prepareFile(file: { buffer: Buffer, name: string }): Promise<PreparedFile> {
        const extension = path.extname(file.name).slice(1).toLowerCase();

        switch (extension) {
            case "txt": {
                return {
                    type: "text",
                    content: file.buffer.toString("utf-8"),
                };
            }

            case "docx": {
                const result = await mammoth.extractRawText({
                    buffer: file.buffer,
                });

                return {
                    type: "text",
                    content: result.value,
                };
            }

            case "xlsx": {
                const fileContent = await getXlsxFileContent(file.buffer);

                return {
                    type: "text",
                    content: fileContent
                        .map(sheet => `${sheet.name}:\n${sheet.content}`).join("\n"),
                };
            }

            case "pdf": {
                // const result = await this.openai.files.create({
                //     file: await OpenAI.toFile(file.buffer, file.name),
                //     purpose: "user_data",
                // });

                return {
                    type: "file",
                    name: file.name,
                    content: `data:application/pdf;base64,${file.buffer.toString("base64")}`,
                };
            }
        }

        throw new Error(`Unsupported file extension: ${extension}`);
    }

    async ask<T>(dto: {
        model?: string;
        responseFormat: {
            schema: z.ZodSchema<T>;
            name: string;
        };
        systemPrompt: string;
        userPrompt: string;
        attachments: {
            name: string;
            buffer: Buffer;
        }[];
    }) {
        const preparedFiles = await Promise.all(
            dto.attachments.map(file => this.prepareFile(file)),
        );

        const messages: OpenAI.ChatCompletionMessageParam[] = [
            {
                role: "system",
                content: dto.systemPrompt,
            },
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: dto.userPrompt,
                    },
                    ...preparedFiles.map<OpenAI.ChatCompletionContentPart>(file => {
                        switch (file.type) {
                            case "text": {
                                return {
                                    type: "text" as const,
                                    text: file.content,
                                };
                            }

                            case "file": {
                                return {
                                    type: "file" as const,
                                    file: {
                                        filename: file.name,
                                        file_data: file.content,
                                    },
                                };
                            }
                        }
                    }),
                ],
            },
        ];

        return await this.chatCompletion({
            model: dto.model,
            messages,
            responseFormat: {
                schema: dto.responseFormat.schema,
                name: dto.responseFormat.name,
            },
        });
    }
}
