import { useState, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { Summary, SummaryFieldKey } from "../types/shippingTypes";
import useSubscribeToPackageUpdates from "../utils/useSubscribeToPackageUpdates";

import {
  PackageDetails,
  ShipmentSummary,
} from ".";

type Props = {
  packageOptions: string[];
};

export const ShipmentItemsForm = ({ packageOptions }: Props) => {
  const [base, setBase] = useState({
    piece: 0,
    volume: 0,
    weight: 0,
  });
  const { getValues } = useFormContext();

  const { updateValue } = useSubscribeToPackageUpdates();

  const handleOnBaseChange = (newBase: number, key: keyof Summary) => {
    setBase((prevValue) => ({
      ...prevValue,
      [key]: newBase < 0 ? 0 : newBase,
    }));
  };

  const handleValueUpdate = useCallback(
    (fieldKey: SummaryFieldKey) => {
      updateValue({
        fieldKey: fieldKey,
        details: getValues("details"),
        base: base,
      });
    },
    [updateValue, base, getValues]
  );

  return (
    <>
      <ShipmentSummary handleOnChange={handleOnBaseChange} />
      <PackageDetails
        base={base}
        packageOptions={packageOptions}
        handleValueUpdate={handleValueUpdate}
      />
    </>
  );
};
