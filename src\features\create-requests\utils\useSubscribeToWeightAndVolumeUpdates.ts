import { useEffect } from "react";
import { useFormContext, UseFormReturn, useWatch } from "react-hook-form";
import { getChargeableWeight } from "./helpers";
import { calculateTotalDensity } from "./calculateTotalPackageDetails";
import { FormData } from "../types/shippingTypes";

const useSubscribeToWeightAndVolumeUpdates = () => {
  const { setValue } = useFormContext<FormData>();

  return useSubscribeToWeightAndVolumeUpdatesWithContext({ setValue });
}

export const useSubscribeToWeightAndVolumeUpdatesWithContext = ({ setValue }: Pick<UseFormReturn<FormData>, "setValue">) => {
  const volume = useWatch({ name: "summary.volume" });
  const weight = useWatch({ name: "summary.weight" });
  const serviceType = useWatch({ name: "service_type" });

  useEffect(() => {
    if (serviceType === "air") {
      const chargeableWeight = getChargeableWeight(
        volume || 0,
        weight || 0,
      );

      setValue("summary.chargeable_weight", chargeableWeight);
    }
  }, [volume, serviceType, setValue, weight]);

  useEffect(() => {
    const newDensity = calculateTotalDensity(
      volume || 0,
      weight || 0,
    );

    setValue("summary.density", newDensity);
  }, [weight, volume, setValue]);
};

export default useSubscribeToWeightAndVolumeUpdates;
