import { z } from "zod";
import { ShippingInfoSchemaBase } from "../utils/validationSchema";

export type Address = {
  is_needed?: boolean;
  city?: string;
  zip_code?: string;
  address?: string;
};

export type Dimension = {
  width?: number | null;
  height?: number | null;
  length?: number | null;
  volume?: number | null;
  weight?: number;
  is_stackable?: boolean;
};

export type Detail = {
  piece: number;
  dimension?: Dimension;
  package_type?: string;
};

export type Summary = {
  piece: number;
  weight: number;
  volume: number;
  density: number;
  chargeable_weight?: number;
};

export type SharedSummaryDetailsKeys = {
  piece: number;
} & Pick<Dimension, 'volume' | 'weight'>

type AdditionalDetails = {
  costs_of_goods?: number | null;
  description_of_goods?: string;
  selected_services?: (string | undefined)[];
  dangerous_goods?: (string | undefined)[];
  services?: string;
  hs_codes?: string | number;
  currency?: string | null;
};

export type ShippingInfo = {
  service_type?: string;
  incoterms?: string;
  origin?: string;
  destination_country?: string;
  pickup?: Address;
  delivery?: Address;
  summary: Summary;
  details?: Detail[];
  additional_details?: AdditionalDetails;
};
export type ShippingInfoWithEmptyFields = Omit<
  ShippingInfo,
  "summary" | "details"
> & {
  summary?: Partial<Summary>;
  details?: Partial<Detail | undefined>[];
};

export type ShippingType = "pickup" | "delivery";

type PackageKeys = keyof Detail | keyof Dimension;

export type SummaryFieldKey = Extract<keyof Summary, PackageKeys>;

export type FormData = z.infer<typeof ShippingInfoSchemaBase>;

export type Base = {
  piece: number;
  weight: number;
  volume: number;
};
