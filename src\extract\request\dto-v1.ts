import { z } from "zod";
import * as Lists from "../../shared/lists";

export const Extract = z.object({
    service_type: z.enum(Lists.services).nullable(),
    incoterms: z.enum(Lists.incoterms).nullable(),
    origin: z.string().nullable(),
    destination_country: z.string().nullable(),

    pickup: z.object({
        is_needed: z.boolean().nullable(),
        city: z.string().nullable(),
        zip_code: z.string().nullable(),
        address: z.string().nullable(),
    }).nullable(),

    delivery: z.object({
        is_needed: z.boolean().nullable(),
        city: z.string().nullable(),
        zip_code: z.string().nullable(),
        address: z.string().nullable(),
    }).nullable(),

    summary: z.object({
        piece: z.number().nullable(),
        weight: z.number().nullable(),
        volume: z.number().nullable(),
        density: z.number().nullable(),
        chargeable_weight: z.number().nullable(),
    }),

    details: z.array(
        z.object({
            piece: z.number().nullable(),
            dimension: z.object({
                width: z.number().nullable(),
                height: z.number().nullable(),
                length: z.number().nullable(),
                volume: z.number().nullable(),
                weight: z.number().nullable(),
                is_stackable: z.boolean().nullable(),
            }),
        }),
    ).nullable(),

    additional_details: z.object({
        description_of_goods: z.string().nullable(),
        hs_codes: z.number().nullable(),
        costs_of_goods: z.number().nullable(),
        services: z.enum(["express", "standard", "economy"]).nullable(),
        selected_services: z.array(z.enum(Lists.additionalServices)).nullable(),
    }),
});
