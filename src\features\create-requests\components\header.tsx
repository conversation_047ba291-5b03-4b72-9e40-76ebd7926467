import { Button, styled } from "@mui/material";
import { Icon } from "../assets";
import styles from "./header.module.css";

type Props = {
  handleSidebarIconClick: () => void;
  sidebarOpen: boolean;
  handleReset: () => void;
  handleSave: () => void;
  saveRequestLoading?: boolean;
};

const StyledButton = styled(Button)({
  color: "white",
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "400",
  borderRadius: "0",
  height: "40px",
});

export const Header = ({
  handleSidebarIconClick,
  sidebarOpen,
  handleReset,
  handleSave,
  saveRequestLoading,
}: Props) => {
  return (
    <div className={styles.header}>
      <button
        onClick={handleSidebarIconClick}
        className={`${styles.base} ${sidebarOpen ? styles.open : styles.visible
          }`}
      >
        <Icon.OpenSidebar />
      </button>
      <div className={styles.container}>
        <StyledButton
          sx={{
            backgroundColor: "rgba(112, 181, 125, 1)",
            width: "186px",
          }}
          onClick={handleReset}
          startIcon={<Icon.Plus />}
        >
          Create new request
        </StyledButton>
        <StyledButton
          sx={{
            width: "120px",
            backgroundColor: "#467c8d",
            color: "white",
          }}
          loading={saveRequestLoading}
          onClick={handleSave}
        >
          Save request
        </StyledButton>
        <StyledButton
          sx={{
            border: "1px solid rgba(26, 26, 26, 1)",
            width: "92px",
            color: "black",
          }}
        >
          Sign in
        </StyledButton>
      </div>
    </div>
  );
};
