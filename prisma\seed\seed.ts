import { PrismaClient } from "@prisma/client";

const prismaClient = new PrismaClient();

const NANOID_NIL = "000000000000000000000";

const users = [
    {
        id: NANOID_NIL,
        email: "<EMAIL>",
        hash: "$2b$10$RT4jPEV5tPwqJZwIub4b4O62/Epc74nOQvsPTSnxLYnpjoW3/hvHK", // qwertyui
    },
];

async function main() {
    await prismaClient.$transaction(async (trx) => {
        await trx.user.createMany({
            data: users,
        });
    });
}

declare global {
    const process: {
        exit(n: number): never;
    };
}

main()
    .then(async () => {
        await prismaClient.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prismaClient.$disconnect();
        process.exit(1);
    });
