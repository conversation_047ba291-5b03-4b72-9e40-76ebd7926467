import type { User } from "@prisma/client";
import type { Config } from "../config";

import { z } from "zod";
import nodemailer from "nodemailer";
import { Injectable } from "../utils";

export interface EmailDto {
    from?: string;
    to: string[];
    subject: string;
    text?: string;
    html?: string;
    cc?: string[];
    bcc?: string[];
    // attachments?: {
    //     filename: string;
    //     content: Buffer | string;
    //     contentType?: string;
    // }[];
}

export class EmailService extends Injectable {
    private config!: Config;

    private transporter!: nodemailer.Transporter;
    private defaultFrom!: string;
    private defaultNewUserNotificationTo!: string;

    override async init1() {
        const host = this.config.getWithValidation(
            "EMAIL_HOST",
            z.string().nonempty(),
        );
        const port = this.config.getWithValidation(
            "EMAIL_PORT",
            z.coerce.number().positive().int(),
        );
        const secure = this.config.getWithValidation(
            "EMAIL_SECURE",
            z.coerce.boolean(),
        );
        const authUser = this.config.getWithValidation(
            "EMAIL_USER",
            z.string().nonempty(),
        );
        const authPassword = this.config.getWithValidation(
            "EMAIL_PASSWORD",
            z.string().nonempty(),
        );

        // Initialize the nodemailer transporter using config
        this.transporter = nodemailer.createTransport({
            host,
            port,
            secure,
            auth: {
                user: authUser,
                pass: authPassword,
            },
        });

        this.defaultFrom = this.config.getWithValidation(
            "EMAIL_DEFAULT_FROM",
            z.string().email(),
        );

        this.defaultNewUserNotificationTo = this.config.getWithValidation(
            "EMAIL_NEW_USER_NOTIFICATION_TO",
            z.string().email(),
        );
    }

    async send(dto: EmailDto) {
        try {
            const mailOptions = {
                from: dto.from ?? this.defaultFrom,
                to: dto.to,
                cc: dto.cc,
                bcc: dto.bcc,
                subject: dto.subject,
                text: dto.text,
                html: dto.html,
                // attachments: dto.attachments,
            };

            await this.transporter.sendMail(mailOptions);
        }
        catch (error) {
            console.error("Error sending email:", error);

            const message = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to send email: ${message}`);
        }
    }

    async sendOtp(dto: {
        to: string;
        otp: string;
    }) {
        await this.send({
            to: [dto.to],
            subject: "✅ Verify your email to activate your Logispot account",
            text: `Your Logispot OTP is ${dto.otp}.`,
            html: `
                Hi there,
                <br />
                Welcome to <strong>Logispot</strong> — your AI co-pilot for smarter logistics.
                
                <br />
                <br />

                To verify your email and activate your account, please use the one-time code below:
                
                <br />
                <br />

                🔐 <strong>Your verification code</strong>: ${dto.otp}
                
                <br />
                <br />

                This code will expire in 15 minutes.
                
                <br />
                <br />

                If you didn't request this, feel free to ignore this email.
                
                <br />
                <br />
                <br />

                Logispot.io Team
                <br />
                <EMAIL>
            `,
        });
    }

    async sendNewUserNotification(user: User) {
        const formattedRegisterDate = user.createdAt.toLocaleString("ru-RU", {
            year: "numeric",
            month: "long",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });

        await this.send({
            to: [this.defaultNewUserNotificationTo],
            subject: "В Logispot зарегистрирован новый пользователь",
            text: `Новый пользователь: ${user.email}`,
            html: `
                <p>Зарегистрирован новый пользователь:</p>
                <p>Email: ${user.email}</p>
                <p>Страна: ${user.country}</p>
                <p>Дата регистрации: ${formattedRegisterDate}</p>
            `,
        });
    }
}
