.resultTextContainer {
    display: flex;
    flex-direction: column;
    min-width: calc(50% - 8px);
    flex-grow: 1;
    white-space: pre-line;
    flex-shrink: 1;
}

.titleResult {
    max-width: 100%;
    flex-shrink: 0;
    width: 100%;
    height: 36px;
    border-radius: 10px;
    background: #F5F5F5;
    display: flex;
    align-items: center;
    padding: 0 7px 0 24px;
    font-size: 12px;
    font-weight: 700;
    overflow-y: auto;
    line-height: 16px;
    justify-content: space-between;
    white-space: nowrap;
}

.titleResult > button {
    align-self: end;
}

.fullTextResultContainer {
    width: 100%;
    height: 135px;
    border-radius: 10px;
    flex-shrink: 1;
    border-width: 1px;
    background: #F5F5F5;
    padding: 9px 7px 9px 24px;
    margin-top: 7px;
    box-sizing: border-box;

    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
}

.fullTextResult {
    overflow-x: auto;
    font-size: 12px;
    font-weight: 400;
    line-height: 140%;
    word-break: break-all;
}

.actionBtns {
    align-self: end;
}