import { useState } from "react";
import { Sidebar, Main } from "../../components";
import { Outlet } from "react-router";

export function IndexLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const toggleSidebar = () => setIsSidebarOpen(v => !v);

  return (
    <>
      <Sidebar handleSidebarClose={toggleSidebar} open={isSidebarOpen} />
      <Main open={isSidebarOpen}>
        <Outlet context={{ isSidebarOpen, toggleSidebar }} />
      </Main>
    </>
  );
};
