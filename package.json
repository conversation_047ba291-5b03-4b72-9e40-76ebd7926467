{"name": "logispot_front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.0", "@mui/icons-material": "^6.4.5", "@mui/material": "^6.4.3", "@types/lodash": "^4.17.16", "lodash": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router": "^7.6.0", "vite-envs": "^4.6.0", "vite-plugin-css-modules": "^0.0.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/css-modules": "^1.0.5", "@types/node": "^22.15.18", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "css-loader": "^7.1.2", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}