import type { z } from "zod";
import type <PERSON>A<PERSON> from "openai";

export interface LlmService {
    chatCompletion<T>(data: {
        model?: string;
        messages: OpenAI.ChatCompletionMessageParam[];
        responseFormat: {
            schema: z.ZodSchema<T>;
            name: string;
        };
    }): Promise<T>;

    uploadFile(file: { buffer: Buffer, name: string }): Promise<{ id: string }>;

    ask<T>(dto: {
        model?: string;
        responseFormat: {
            schema: z.ZodSchema<T>;
            name: string;
        };
        systemPrompt: string;
        userPrompt: string;
        attachments: {
            name: string;
            buffer: Buffer;
        }[];
    }): Promise<T>
}
