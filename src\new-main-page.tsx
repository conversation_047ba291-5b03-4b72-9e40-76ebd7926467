import { <PERSON><PERSON>, <PERSON>ircular<PERSON><PERSON>ress, Icon<PERSON>utton, styled } from "@mui/material";
import { useCallback, useRef, useState } from "react";
import { CustomAlert, Main, Sidebar } from "./components";
import { Icon } from "./features/create-requests/assets";
import styles from "./new-main-page.module.css";

import {
  Main as MainContentMain,
  CopyButton,
} from "./features/create-requests/components"
import { useSendPrompt } from "./features/create-requests/data/useSendPrompt";
import { FormWrapper2 } from "./features/create-requests/components/form-wrapper-2";

const StyledButton = styled(Button)({
  color: "white",
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "400",
  borderRadius: "0",
  height: "40px",
});

export type TResultText = {
  title: string;
  mainContent: string;
};

export function NewMainPage() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const formRef = useRef<HTMLFormElement>(null);
  const [inputText, setInputText] = useState("");

  const handleReset = useCallback(() => {
    if (formRef.current) {
      formRef.current.reset();
      setInputText("");
    }
  }, []);

  const [isLoading, setIsLoading] = useState(false);

  const handleSave = useCallback(async () => {
    if (formRef.current) {
      setIsLoading(true);
      try {
        await formRef.current.submitForm();
      } finally {
        setIsLoading(false)
      }
    }
  }, []);

  const { sendPrompt, loading, error, data } = useSendPrompt();
  const [resultText, setResultText] = useState({
    title: "",
    mainContent: "",
  });

  const handleResultMainTextChange = useCallback((text: TResultText) => {
    setResultText(text);
  }, []);

  const handleSendButtonClick = async () => {
    await sendPrompt(inputText);

    if (formRef.current) {
      formRef.current.reset();
    }
  };

  const [controlledError, setControlledError] = useState<string | null>(null);
  const pasteAreaRef = useRef<HTMLTextAreaElement | null>(null);

  const handleTextClear = () => {
    setInputText("");
  };

  const handlePaste = async () => {
    setControlledError(null);
    try {
      const clipboardText = await navigator.clipboard.readText();
      const newText =
        inputText.trim() === "" ? clipboardText : inputText + "\n" + clipboardText;

      setInputText(newText);
    } catch {
      setControlledError("Failed to read clipboard contents");
    }
  };

  return (
    <>
      <Sidebar handleSidebarClose={() => setIsSidebarOpen(false)} open={isSidebarOpen} />
      <Main open={isSidebarOpen}>
        {/* header */}
        <div className={styles.header}>
          <button
            onClick={() => setIsSidebarOpen(true)}
            className={`${styles.base} ${isSidebarOpen ? styles.open : styles.visible
              }`}
          >
            <Icon.OpenSidebar />
          </button>
          <div className={styles.container}>
            <StyledButton
              sx={{
                backgroundColor: "rgba(112, 181, 125, 1)",
                width: "186px",
              }}
              onClick={handleReset}
              startIcon={<Icon.Plus />}
            >
              Create new request
            </StyledButton>
            <StyledButton
              sx={{
                width: "120px",
                backgroundColor: "#467c8d",
                color: "white",
              }}
              loading={isLoading}
              onClick={handleSave}
            >
              Save request
            </StyledButton>
            <StyledButton
              sx={{
                border: "1px solid rgba(26, 26, 26, 1)",
                width: "92px",
                color: "black",
              }}
            >
              Sign in
            </StyledButton>
          </div>
        </div>

        {/* main content */}
        <CustomAlert message={error} severity="error" />
        <MainContentMain open={isSidebarOpen}>
          <div className={styles.contentContainer}>
            <div className={styles.textContainer}>
              <div className={styles.label}>
                Paste here the text of request for cargo transportation
              </div>

              <button
                onClick={handleSendButtonClick}
                disabled={loading || !inputText}
                className={styles.sendButton}
              >
                {loading ? (
                  <CircularProgress
                    size={16}
                    style={{ position: "absolute", color: "white" }}
                  />
                ) : (
                  <Icon.Send />
                )}
              </button>

              <div className={styles.textInputsContainer}>
                <CustomAlert message={controlledError} severity="error" />
                <div className={styles.textIntoInputContainer}>
                  <div className={styles.textFieldContainer}>
                    <textarea
                      className={styles.textField}
                      ref={pasteAreaRef}
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                    ></textarea>
                    <IconButton
                      size="small"
                      sx={{
                        position: "absolute",
                        margin: "2px 2px",
                        right: 0,
                        top: 0,
                      }}
                      onClick={handleTextClear}
                    >
                      <Icon.Close />
                    </IconButton>
                  </div>

                  <Button
                    disableElevation
                    onClick={handlePaste}
                    startIcon={<Icon.Paste />}
                    size="small"
                    variant="text"
                    sx={{
                      fontSize: "12px",
                      textTransform: "lowercase",
                      cursor: "pointer",
                      fontWeight: "400",
                      lineHeight: '16px',
                      marginTop: "5px",
                    }}
                  >
                    paste
                  </Button>
                </div>

                <div className={styles.resultTextContainer}>
                  <div className={styles.titleResult}>
                    <div style={{ flexShrink: 2, overflow: "auto" }}>{resultText.title}</div>
                    <CopyButton text={resultText.title} disabled={!resultText.title} />
                  </div>

                  <div className={styles.fullTextResultContainer}>
                    <div className={styles.fullTextResult}>{resultText.mainContent}</div>
                    <div className={styles.actionBtns}>
                      <CopyButton text={resultText.mainContent} disabled={!resultText.mainContent} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <FormWrapper2
              data={data}
              ref={formRef}
              setResultText={handleResultMainTextChange}
            />
          </div>
        </MainContentMain>
      </Main>
    </>
  );
}
