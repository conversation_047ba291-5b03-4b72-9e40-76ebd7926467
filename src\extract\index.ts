import type { LlmService } from "../llm";

import { Injectable } from "../utils";
import * as RequestDtoV1 from "./request/dto-v1";
import * as RequestDtoV2 from "./request/dto-v2";
import * as RequestPrompts from "./request/prompts";
import * as TariffDtoV1 from "./tariff/dto-v1";
import * as TariffPrompts from "./tariff/prompts";

export class ExtractService extends Injectable {
    private readonly llmService!: LlmService;

    async extractRequestV1(data: {
        text: string;
    }) {
        return await this.llmService.chatCompletion({
            messages: [
                {
                    role: "system",
                    content: RequestPrompts.v1,
                },
                {
                    role: "user",
                    content: data.text,
                },
            ],
            responseFormat: {
                schema: RequestDtoV1.Extract,
                name: "extract",
            },
        });
    }

    async extractRequestV2(data: {
        text: string;
    }) {
        const [
            metadata,
            digits,
            services,
        ] = await Promise.all([
            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: RequestPrompts.v2.metadata,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: RequestDtoV2.ExtractMetadata,
                    name: "metadata",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: RequestPrompts.v2.digits,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: RequestDtoV2.ExtractDigits,
                    name: "digits",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: RequestPrompts.v2.services,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: RequestDtoV2.ExtractServices,
                    name: "services",
                },
            }),
        ]);

        return {
            metadata,
            digits,
            services,
        };
    }

    async extractRequestV3(data: {
        text: string;
        files: {
            name: string;
            buffer: Buffer;
        }[];
    }) {
        const [
            metadata,
            digits,
            services,
        ] = await Promise.all([
            this.llmService.ask({
                systemPrompt: RequestPrompts.v3.metadata,
                userPrompt: data.text,
                attachments: data.files,
                responseFormat: {
                    schema: RequestDtoV2.ExtractMetadata,
                    name: "metadata",
                },
            }),

            this.llmService.ask({
                systemPrompt: RequestPrompts.v3.digits,
                userPrompt: data.text,
                attachments: data.files,
                responseFormat: {
                    schema: RequestDtoV2.ExtractDigits,
                    name: "digits",
                },
            }),

            this.llmService.ask({
                systemPrompt: RequestPrompts.v3.services,
                userPrompt: data.text,
                attachments: data.files,
                responseFormat: {
                    schema: RequestDtoV2.ExtractServices,
                    name: "services",
                },
            }),
        ]);

        return {
            metadata,
            digits,
            services,
        };
    }

    async extractTariffV1(data: {
        text: string;
        files: {
            name: string;
            buffer: Buffer;
        }[];
    }) {
        return await this.llmService.ask({
            systemPrompt: TariffPrompts.v1,
            userPrompt: data.text,
            attachments: data.files,
            responseFormat: {
                schema: TariffDtoV1.Extract,
                name: "tariff",
            },
        });
    }
}
