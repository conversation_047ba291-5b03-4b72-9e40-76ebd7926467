import {
  FormControl,
  MenuItem,
  SelectChangeEvent,
  SxProps,
  Theme,
  Select,
  FormHelperText,
} from "@mui/material";
import { CustomizedInputLabel } from "./customized-input-label";
import { CustomCheckbox } from "./custom-checkbox";
import { ControllerFieldState } from "react-hook-form";

type Props = {
  label: string;
  options: readonly string[];
  additionalStyling?: SxProps<Theme> | undefined;
  value?: string | string[];
  placeholder?: string;
  multiply?: boolean;
  onChange?: (e: SelectChangeEvent<string>) => void;
  maxWidth?: number;
  fieldState: ControllerFieldState;
};

export const CustomSelectWithLabel = ({
  label,
  options,
  additionalStyling,
  value,
  onChange,
  multiply,
  maxWidth,
  placeholder,
  fieldState,
}: Props) => {
  return (
    <FormControl
      fullWidth
      sx={{
        flexGrow: 1,
        maxWidth: maxWidth,
        ...additionalStyling,
      }}
      error={!!fieldState.error}
      size="small"
    >
      <CustomizedInputLabel
        shrink
        id={`${label}-label`}
        sx={{ left: "-15px", marginBottom: "8px" }}
        fontWeight={500}
      >
        {label}
      </CustomizedInputLabel>
      <Select
        labelId={`${label}-label`}
        id={`${label}-label-select`}
        fullWidth
        multiple={multiply}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        value={value as any}
        onChange={(e) => {
          onChange?.(e);
        }}
        displayEmpty
        renderValue={(selected) => {
          if (selected?.length === 0 || !selected) {
            return <span>{placeholder}</span>;
          }

          return Array.isArray(selected) ? selected.join(", ") : selected;
        }}
        sx={{
          height: "40px",
          borderRadius: "0",
          fontSize: "12px",
          " &.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "2px solid green",
          },
          marginTop: "8px",
          marginBottom: "4px",
        }}
        MenuProps={{
          disableScrollLock: true,
        }}
        slotProps={{
          input: {
            sx: {
              textTransform: "capitalize",
            },
          },
        }}
      >
        {!multiply && (
          <MenuItem
            sx={{
              fontSize: "12px",
            }}
          >
            <em>None</em>
          </MenuItem>
        )}
        {options.map((item) => (
          <MenuItem
            key={item}
            value={item}
            sx={{
              textTransform: "capitalize",
            }}
          >
            <CustomCheckbox
              formName={item}
              checked={
                Array.isArray(value)
                  ? value?.includes(item) ?? false
                  : value === item
              }
              label={item}
            />
          </MenuItem>
        ))}
      </Select>

      {fieldState?.error?.message && (
        <FormHelperText error> {fieldState.error.message}</FormHelperText>
      )}
    </FormControl>
  );
};
