import type { NextFunction, Request, Response } from "express";

import { handleError } from "../utils";
import { SignalError } from "./signal";

export class NotFoundSignalError extends SignalError {}
export class BadRequestSignalError extends SignalError {}
export class AlreadyExistsSignalError extends SignalError {}
export class UnauthorizedSignalError extends SignalError {}
export class ForbiddenSignalError extends SignalError {}
export class InternalServerErrorSignalError extends SignalError {}

export function createSignalErrorHandler() {
    return (err: Error, _: Request, res: Response, next: NextFunction) => {
        handleError(err, [
            [NotFoundSignalError, (err) => {
                res.status(404).send(err.toString());
            }],
            [BadRequestSignalError, (err) => {
                res.status(400).send(err.toString());
            }],
            [AlreadyExistsSignalError, (err) => {
                res.status(409).send(err.toString());
            }],
            [UnauthorizedSignalError, (err) => {
                res.status(401).send(err.toString());
            }],
            [ForbiddenSignalError, (err) => {
                res.status(403).send(err.toString());
            }],
            [InternalServerErrorSignalError, (err) => {
                res.status(500).send(err.toString());
            }],

            [SignalError, (err) => {
                res.status(500).send(`Unhandled signal: ${err.toString()}`);
            }],

            [Error, (err) => next(err)],
        ]);
    };
}
