import { useCallback, useEffect } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { TextFieldWithLabel } from "../../../components";
import { ShippingInfo, SummaryFieldKey } from "../types/shippingTypes";
import styles from "./shipment-summary.module.css";
import {
  calculateTotalDensity,
  getTotalFieldsSum,
} from "../utils/calculateTotalPackageDetails";
import { Summary } from "../types/shippingTypes";
import useSubscribeToWeightAndVolumeUpdates from "../utils/useSubscribeToWeightAndVolumeUpdates";

import { GenericErrorHelper } from "./generic-error-helper";

type Props = {
  handleOnChange: (newValue: number, key: keyof Summary) => void;
};

export const ShipmentSummary = ({ handleOnChange }: Props) => {
  const { getValues, setValue, trigger } = useFormContext<ShippingInfo>();

  useSubscribeToWeightAndVolumeUpdates();

  const result = useWatch({
    name: ["summary.weight", "summary.volume"],
  });

  const serviceType = useWatch({
    name: ["service_type"],
  })[0];

  useEffect(() => {
    const density = calculateTotalDensity(
      result[0],
      result[1],
    );

    setValue("summary.density", density);
  }, [result, setValue]);

  const handleChange = useCallback(
    (newSummary: number, key: SummaryFieldKey) => {
      const details = getValues("details");
      const summedDetailsPieces = getTotalFieldsSum(key, details);

      handleOnChange(newSummary - summedDetailsPieces, key);
    },
    [getValues, handleOnChange]
  );

  return (
    <div className={styles.shipmentSummaryContainer}>
      <h3 className={styles.shipmentSummaryLabel}>Shipment Summary</h3>

      <div className={styles.inputsContainer}>
        <TextFieldWithLabel
          handleOnBlur={() => trigger("summary.piece")}
          handleOnChange={(result) => handleChange(result, "piece")}
          errorWrapper={
            <GenericErrorHelper
              fieldKey="summary.piece"
              getFieldValue={() =>
                getTotalFieldsSum("piece", getValues(`details`))
              }
            />
          }
          label="Piece (pcs)"
          formName="summary.piece"
          type="number"
        />
        <TextFieldWithLabel
          label="Total weight (kg)"
          formName="summary.weight"
          handleOnChange={(result) => handleChange(result, "weight")}
          errorWrapper={
            <GenericErrorHelper
              fieldKey="summary.weight"
              getFieldValue={() =>
                getTotalFieldsSum("weight", getValues(`details`))
              }
            />
          }
          type="number"
          handleOnBlur={() => trigger("summary.weight")}
        />
        <TextFieldWithLabel
          label="Volume (m³)"
          formName="summary.volume"
          type="number"
          handleOnChange={(result) => handleChange(result, "volume")}
          errorWrapper={
            <GenericErrorHelper
              fieldKey="summary.volume"
              getFieldValue={() =>
                getTotalFieldsSum("volume", getValues(`details`))
              }
            />
          }
          handleOnBlur={() => trigger("summary.volume")}
        />
        <TextFieldWithLabel label="Density" locked formName="summary.density" type="number" />

        {serviceType === "air" && (
          <TextFieldWithLabel
            label="Chargeable weight"
            locked
            type="number"
            formName="summary.chargeable_weight"
          />
        )}
      </div>
    </div>
  );
};
