import type { PrismaClient, RequestPackageType, RequestStatus } from "@prisma/client";

import { Injectable } from "../utils";
import { ForbiddenSignalError, NotFoundSignalError } from "../error/http";

export class RequestService extends Injectable {
    private readonly prisma!: PrismaClient;

    async create(dto: {
        userId: string;

        text: string;

        service: string | null;
        incoterms: string | null;

        origin: {
            country: string | null;
            city: string | null;
            address: string | null;
            zipcode: string | null;
            isPickupRequired: boolean;
        };

        destination: {
            country: string | null;
            city: string | null;
            address: string | null;
            zipcode: string | null;
            isDeliveryRequired: boolean;
        };

        descriptionOfGoods: string | null;
        hsCodes: string | null;

        costOfGoods: number | null;
        currency: string | null;

        additionalServices: string[];
        dangerousGoods: string[];

        quantity: number;
        weight: number;
        volume: number;
        chargeableWeight: number;

        packages: {
            quantity: number;
            length: number;
            width: number;
            height: number;
            weight: number;
            type: RequestPackageType | null;
            isStackable: boolean;
        }[];
    }) {
        const request = await this.prisma.$transaction(async trx => {
            const userRequestCounter = await trx.userRequestCounter.upsert({
                where: {
                    userId: dto.userId,
                },
                update: {
                    count: {
                        increment: 1,
                    },
                },
                create: {
                    userId: dto.userId,
                    count: 1,
                },
            });

            return await this.prisma.request.create({
                data: {
                    userId: dto.userId,

                    ordinal: userRequestCounter.count,

                    text: dto.text,

                    service: dto.service,
                    incoterms: dto.incoterms,

                    originCountry: dto.origin.country,
                    originCity: dto.origin.city,
                    originAddress: dto.origin.address,
                    originZipcode: dto.origin.zipcode,
                    isPickupRequired: dto.origin.isPickupRequired,

                    destinationCountry: dto.destination.country,
                    destinationCity: dto.destination.city,
                    destinationAddress: dto.destination.address,
                    destinationZipcode: dto.destination.zipcode,
                    isDeliveryRequired: dto.destination.isDeliveryRequired,

                    descriptionOfGoods: dto.descriptionOfGoods,
                    hsCodes: dto.hsCodes,

                    costOfGoods: dto.costOfGoods ?? 0,
                    currency: dto.currency,

                    additionalServices: dto.additionalServices,
                    dangerousGoods: dto.dangerousGoods,

                    quantity: dto.quantity,
                    weight: dto.weight,
                    volume: dto.volume,
                    chargeableWeight: dto.chargeableWeight,

                    packages: {
                        create: dto.packages.map(pkg => ({
                            quantity: pkg.quantity,
                            length: pkg.length,
                            width: pkg.width,
                            height: pkg.height,
                            weight: pkg.weight,
                            type: pkg.type,
                        })),
                    },
                },
            });
        });

        return {
            id: request.id,
        };
    }

    async update(dto: {
        userId: string;

        id: string;

        text: string;

        service: string | null;
        incoterms: string | null;

        origin: {
            country: string | null;
            city: string | null;
            address: string | null;
            zipcode: string | null;
            isPickupRequired: boolean;
        };

        destination: {
            country: string | null;
            city: string | null;
            address: string | null;
            zipcode: string | null;
            isDeliveryRequired: boolean;
        };

        descriptionOfGoods: string | null;
        hsCodes: string | null;

        costOfGoods: number | null;
        currency: string | null;

        additionalServices: string[];
        dangerousGoods: string[];

        quantity: number;
        weight: number;
        volume: number;
        chargeableWeight: number;

        packages: {
            quantity: number;
            length: number;
            width: number;
            height: number;
            weight: number;
            type: RequestPackageType | null;
            isStackable: boolean;
        }[];
    }) {
        const request = await this.prisma.$transaction(async trx => {
            const request = await trx.request.findUniqueOrThrow({
                where: {
                    id: dto.id,
                },
            });

            if (request.userId !== dto.userId) {
                throw new ForbiddenSignalError("not author");
            }

            return await this.prisma.request.update({
                where: { id: dto.id },
                data: {
                    userId: dto.userId,

                    text: dto.text,

                    service: dto.service,
                    incoterms: dto.incoterms,

                    originCountry: dto.origin.country,
                    originCity: dto.origin.city,
                    originAddress: dto.origin.address,
                    originZipcode: dto.origin.zipcode,
                    isPickupRequired: dto.origin.isPickupRequired,

                    destinationCountry: dto.destination.country,
                    destinationCity: dto.destination.city,
                    destinationAddress: dto.destination.address,
                    destinationZipcode: dto.destination.zipcode,
                    isDeliveryRequired: dto.destination.isDeliveryRequired,

                    descriptionOfGoods: dto.descriptionOfGoods,
                    hsCodes: dto.hsCodes,

                    costOfGoods: dto.costOfGoods ?? 0,
                    currency: dto.currency,

                    additionalServices: dto.additionalServices,
                    dangerousGoods: dto.dangerousGoods,

                    quantity: dto.quantity,
                    weight: dto.weight,
                    volume: dto.volume,
                    chargeableWeight: dto.chargeableWeight,

                    packages: {
                        deleteMany: {},
                        create: dto.packages.map(pkg => ({
                            quantity: pkg.quantity,
                            length: pkg.length,
                            width: pkg.width,
                            height: pkg.height,
                            weight: pkg.weight,
                            type: pkg.type,
                            isStackable: pkg.isStackable,
                        })),
                    },
                },
            });
        });

        return {
            id: request.id,
        };
    }

    async updateStatus(dto: {
        userId: string;

        id: string;
        status: RequestStatus;
    }) {
        const request = await this.prisma.request.findUniqueOrThrow({
            where: {
                id: dto.id,
            },
        });

        if (request.userId !== dto.userId) {
            throw new ForbiddenSignalError("not author");
        }

        await this.prisma.request.update({
            where: { id: dto.id },
            data: {
                status: dto.status,
            },
        });
    }

    async getMany(dto: {
        userId: string;

        searchQuery?: string;

        origin?: string;
        destination?: string;

        service?: string;

        from?: Date;
        to?: Date;
    }) {
        const requests = await this.prisma.request.findMany({
            where: {
                userId: dto.userId,

                originCountry: dto.origin,
                destinationCountry: dto.destination,

                service: dto.service,

                createdAt: {
                    gte: dto.from,
                    lte: dto.to,
                },

                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return requests;
    }

    async getOne(dto: {
        userId: string;

        id: string;
    }) {
        const request = await this.prisma.request.findUniqueOrThrow({
            where: {
                id: dto.id,
            },
            include: {
                packages: true,
            },
        });

        if (request.userId !== dto.userId) {
            throw new ForbiddenSignalError("not author");
        }

        return request;
    }

    async getAvailableLocations(dto: {
        userId: string;
    }) {
        const [
            origins,
            destinations,
        ] = await Promise.all([
            this.prisma.request.findMany({
                where: {
                    userId: dto.userId,
                    deletedAt: null,
                },
                select: {
                    originCountry: true,
                },
                distinct: ["originCountry"],
            })
                .then(requests => requests.map(request => request.originCountry)),

            this.prisma.request.findMany({
                where: {
                    userId: dto.userId,
                    deletedAt: null,
                },
                select: {
                    destinationCountry: true,
                },
                distinct: ["destinationCountry"],
            })
                .then(requests => requests.map(request => request.destinationCountry)),
        ]);

        return {
            origins,
            destinations,
        };
    }

    async softDelete(dto: {
        id: string;
        userId: string;
    }) {
        await this.prisma.$transaction(async trx => {
            const request = await trx.request.findUnique({
                where: {
                    id: dto.id,
                },
            });

            if (!request) {
                throw new NotFoundSignalError("request");
            }

            if (request.userId !== dto.userId) {
                throw new ForbiddenSignalError("not author");
            }

            await trx.request.update({
                where: {
                    id: dto.id,
                },
                data: {
                    deletedAt: new Date(),
                },
            });
        });

        return true;
    }

    async hardDelete(dto: {
        id: string;
    }) {
        await this.prisma.request.delete({
            where: {
                id: dto.id,
            },
        });

        return true;
    }
}
