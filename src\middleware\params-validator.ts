import type { NextFunction, Request, RequestHandler, Response } from "express";
import type { z } from "zod";

export const paramsValidator = (schema: z.ZodSchema): RequestHandler => {
    return (
        req: Request,
        res: Response,
        next: NextFunction,
    ): void => {
        const validationResult = schema.safeParse(req.params);

        if (!validationResult.success) {
            res
                .status(400)
                .json({
                    message: "Validation failed",
                    errors: validationResult.error.errors,
                });

            return;
        }

        req.params = validationResult.data;

        next();
    };
};
