import ExcelJS from "exceljs";

function cellValueToText(val: ExcelJS.CellValue): string {
    if (val == null) return ""; // null or undefined
    if (typeof val === "string") return val;
    if (typeof val === "number") return String(val);
    if (typeof val === "boolean") return val ? "TRUE" : "FALSE";
    if (val instanceof Date) return val.toISOString();

    // objects (formula, hyperlink, richText, result, etc.)
    if (typeof val === "object") {
        // formula object: { formula: 'A1+B1', result: 3 }
        if ("formula" in val) {
        // prefer computed result if available, otherwise show formula
            return val.result != null ? cellValueToText(val.result) : `=${val.formula}`;
        }

        // hyperlink object: { text: 'label', hyperlink: 'http://...' }
        if ("hyperlink" in val) {
            return val.text || val.hyperlink;
        }

        // richText: [{text:'a'},{text:'b',font:{...}}]
        if ("richText" in val && Array.isArray(val.richText)) {
            return val.richText.map(p => p.text || "").join("");
        }

        // some cells use { text: 'something' }
        if ("text" in val && typeof val.text === "string") return val.text;

        if ("error" in val) {
            return val.error;
        }

        // result fallback
        if ("result" in val) return cellValueToText(val.result);

        // last-resort: JSON representation (short)
        try {
            return JSON.stringify(val);
        } catch (e) {
            return String(val);
        }
    }

    return String(val);
}

export async function getXlsxFileContent(buffer: Buffer) {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);

    const result: { name: string, content: string }[] = [];

    workbook.eachSheet((worksheet) => {
        const lines: string[] = [];

        worksheet.eachRow({ includeEmpty: false }, (row) => {
            const cells: string[] = [];

            row.eachCell({ includeEmpty: false }, (cell) => {
                cells.push(cellValueToText(cell.value));
            });

            // join cells with a tab (or use comma, or other delimiter)
            lines.push(cells.join(";"));
        });

        result.push({
            name: worksheet.name,
            content: lines.join("\n"),
        });
    });

    return result;
}
