import { roundUpToTwoDecimals } from "../../../utils/roundUpToTwoDecimals";
import { Dimension, ShippingInfo, ShippingType } from "../types/shippingTypes";

export const validateIncotermsPickupCity = (data: ShippingInfo) =>
  data.incoterms !== "EXW" || data.pickup?.city;

export const validateIncotermsAddress = (data: ShippingInfo) =>
  data.incoterms !== "EXW" || data.pickup?.address;

export const isCityValid = (data: ShippingInfo, type: ShippingType) =>
  !data[type]?.is_needed || !!data[type]?.city;

export const isTotalEqualToDetailed = ({
  total,
  detailed,
}: {
  total?: number | null;
  detailed?: number;
}) => {
  if (detailed) {
    if (!total) {
      return false;
    }

    return roundUpToTwoDecimals(total) === roundUpToTwoDecimals(detailed);
  }

  return true;
};

export const validateVolume = (data?: Dimension) => {
  const volumeFromDimensions = getVolumeFromDimensions(data);
  return isTotalEqualToDetailed({
    total: data?.volume,
    detailed: volumeFromDimensions,
  });
};

export const getVolumeFromDimensions = (data?: Dimension, rounded?: boolean) => {
  if (data?.width && data.height && data.length) {
    const calculatedVolumeInCm3 = data.width * data.height * data.length;

    const calculatedVolumeInM3 = calculatedVolumeInCm3 / 1_000_000;

    if (rounded) {
      return roundUpToTwoDecimals(calculatedVolumeInM3)
    }

    return calculatedVolumeInM3;
  }

  return undefined;
};
