import { Link } from "@mui/material";
import {
  FieldValues,
  Path,
  PathValue,
  useFormContext,
  useFormState,
} from "react-hook-form";
import _ from "lodash";

type Props<T> = {
  fieldKey: Path<T>;
  getFieldValue?: () => PathValue<T, Path<T>>;
  onUpdate?: () => void;
};

export const GenericErrorHelper = <T extends FieldValues>({
  fieldKey,
  getFieldValue,
  onUpdate,
}: Props<T>) => {
  const { setValue, trigger } = useFormContext<T>();
  const { errors } = useFormState<T>({ name: fieldKey });
  const error = _.get(errors, fieldKey);

  if (!error) {
    return null;
  }

  const handleOnApplyClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (getFieldValue) {
      const calculatedValue = getFieldValue();
      setValue(fieldKey, calculatedValue);
      trigger(fieldKey);
      onUpdate?.();
    }
  };

  if (error.type !== "custom" && error.message) {
    return String(error.message);
  }

  return (
    <>
      <span>{String(error.message)}</span>
      <br />
      {getFieldValue && (
        <Link color="error" component="button" onClick={handleOnApplyClick}>
          Apply
        </Link>
      )}
    </>
  );
};
