import { IconButton } from "@mui/material";
import { memo } from "react";
import {
  FieldValues,
  useFormContext,
} from "react-hook-form";
import {
  TextFieldWithLabel,
  ControlledCustomSelect,
  ControlledCustomCheckbox,
} from "../../../components";
import { SummaryFieldKey } from "../types/shippingTypes";
import { getVolumeFromDimensions } from "../utils/validationFunctions";
import { Icon } from "../assets";
import styles from "./package-item.module.css";

import { GenericErrorHelper } from "./generic-error-helper";

type Props = {
  onChange: ({ fieldKey }: { fieldKey: SummaryFieldKey }) => void;
  index: number;
  remove: (index: number) => void;
  options: readonly string[];
};

export const PackageItem = memo(
  ({
    index,
    remove,
    onChange,
    options,
  }: Props) => {
    const { getValues, setValue, trigger } = useFormContext<FieldValues>();

    return (
      <div className={styles.inputsContainer}>
        <TextFieldWithLabel
          label="Piece (pcs)"
          formName={`details.${index}.piece.`}
          maxWidth={188}
          type="number"
          handleOnChange={() => {
            onChange({ fieldKey: "piece" });
            onChange({ fieldKey: "volume" });
            onChange({ fieldKey: "weight" });
          }}
        />
        <TextFieldWithLabel
          label="Dimensions (cm)"
          maxWidth={290}
          type="number"
          handleOnChange={() => {
            const newDimension = getValues(`details.${index}.dimension`);
            const newVolume = getVolumeFromDimensions(newDimension);
            if (newVolume) {
              setValue(`details.${index}.dimension.volume`, newVolume);
              onChange({ fieldKey: "volume" });
            }
          }}
          formName={[
            `details.${index}.dimension.length`,
            `details.${index}.dimension.width`,
            `details.${index}.dimension.height`,
          ]}
        />

        <TextFieldWithLabel
          label="Volume (m³)"
          type="number"
          formName={`details.${index}.dimension.volume`}
          maxWidth={86}
          errorWrapper={
            <GenericErrorHelper
              onUpdate={() => {
                trigger?.("summary.volume");
                onChange({ fieldKey: "volume" });
              }}
              fieldKey={`details.${index}.dimension.volume`}
              getFieldValue={() => {
                const dimensions = getValues(`details.${index}.dimension`);

                return getVolumeFromDimensions(dimensions);
              }}
            />
          }
          handleOnChange={() => {
            onChange({ fieldKey: "volume" });
            trigger?.(`details.${index}.dimension.volume`);
          }}
        />

        <TextFieldWithLabel
          type="number"
          label="Weight (kg)"
          formName={`details.${index}.dimension.weight`}
          maxWidth={89}
          handleOnChange={() => onChange({ fieldKey: "weight" })}
        />

        <ControlledCustomSelect
          name={`details.${index}.package_type`}
          label="Package type"
          options={options}
          withLabel
          maxWidth={187}
          placeholder="Choose package"
        />

        <div className={styles.alignContainer}>
          <ControlledCustomCheckbox
            formName={`details.${index}.dimension.is_stackable`}
            label="non stackable"
          />
        </div>

        <div className={styles.alignContainer}>
          <IconButton onClick={() => remove(index)}>
            <Icon.Trash />
          </IconButton>
        </div>
      </div>
    );
  }
);
