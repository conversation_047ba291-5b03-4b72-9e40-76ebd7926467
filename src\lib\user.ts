import { z } from "zod";

export type LocalStorageUser = z.infer<typeof LocalStorageUser>;
export const LocalStorageUser = z.object({
  id: z.string(),
  email: z.string().email(),
});

export function getLocalStorageUser(): LocalStorageUser | null {
  const localStorageUser = localStorage.getItem("user");

  if (!localStorageUser) {
    return null;
  }

  try {
    const jsonUser = JSON.parse(localStorageUser);
    const user = LocalStorageUser.safeParse(jsonUser);

    if (user.success) {
      return user.data;
    }

    localStorage.removeItem("user");
    return null;
  }
  catch {
    localStorage.removeItem("user");
    return null;
  }
}

export function setLocalStorageUser(user: LocalStorageUser) {
  localStorage.setItem("user", JSON.stringify(user));
}

export function removeLocalStorageUser() {
  localStorage.removeItem("user");
}


