import { z } from "zod";

export const transportType = z.enum([
    "air",
    "road",
    "sea",
    "rail",
]);

export const chargeType = z.enum([
    "terminal",
    "doc",
    "awb",
    "custom_clearance",
    "pickup",
    "other",
]);

export const chargeUnit = z.enum([
    "kg",
    "piece",
    "shipment",
]);

export const localChargeType = z.enum([
    "terminal",
    "doc",
    "awb",
    "custom_clearance",
    "pickup",
    "other",
]);

export const localChargeUnit = z.enum([
    "kg",
    "piece",
    "shipment",
]);

export const Extract = z.object({
    variants: z.array(
        z.object({
            transportType,

            origin: z.string().nonempty().nullable(),
            destination: z.string().nonempty().nullable(),

            agentName: z.string().nonempty().nullable(),
            carrierName: z.string().nonempty().nullable(),

            charges: z.array(
                z.object({
                    type: chargeType.nullable(),
                    unit: chargeUnit.nullable(),
                    rate: z.number().nonnegative(),
                    currency: z.string().nonempty(),
                }),
            ),

            localCharges: z.array(
                z.object({
                    type: localChargeType.nullable(),
                    unit: localChargeUnit.nullable(),
                    rate: z.number().nonnegative(),
                    currency: z.string().nonempty(),
                }),
            ),
        }),
    ),
});
