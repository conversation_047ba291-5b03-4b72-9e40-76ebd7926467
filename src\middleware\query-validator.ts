import type { NextFunction, Request, RequestHandler, Response } from "express";
import type { z } from "zod";

export const queryValidator = (schema: z.ZodSchema): RequestHandler => {
    return (
        req: Request,
        res: Response,
        next: NextFunction,
    ): void => {
        const validationResult = schema.safeParse(req.query);

        if (!validationResult.success) {
            res
                .status(400)
                .json({
                    message: "Validation failed",
                    errors: validationResult.error.errors,
                });

            return;
        }

        Object.assign(req, { queryParams: validationResult.data });

        next();
    };
};
