/*
  Warnings:

  - You are about to drop the column `chargeableWeight` on the `requests` table. All the data in the column will be lost.
  - You are about to drop the column `destination` on the `requests` table. All the data in the column will be lost.
  - You are about to drop the column `origin` on the `requests` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `requests` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `user_request_counters` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[user_id]` on the table `user_request_counters` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `chargeable_weight` to the `requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `text` to the `requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `user_request_counters` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "RequestPackageType" AS ENUM ('box', 'crate', 'pallet');

-- DropForeignKey
ALTER TABLE "requests" DROP CONSTRAINT "requests_userId_fkey";

-- DropIndex
DROP INDEX "user_request_counters_userId_key";

-- AlterTable
ALTER TABLE "requests" DROP COLUMN "chargeableWeight",
DROP COLUMN "destination",
DROP COLUMN "origin",
DROP COLUMN "userId",
ADD COLUMN     "additional_services" TEXT[],
ADD COLUMN     "chargeable_weight" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "cost_of_goods" DECIMAL(65,30),
ADD COLUMN     "currency" TEXT,
ADD COLUMN     "dangerous_goods" TEXT[],
ADD COLUMN     "description_of_goods" TEXT,
ADD COLUMN     "destination_address" TEXT,
ADD COLUMN     "destination_city" TEXT,
ADD COLUMN     "destination_country" TEXT,
ADD COLUMN     "destination_zipcode" TEXT,
ADD COLUMN     "hs_codes" TEXT,
ADD COLUMN     "incoterms" TEXT,
ADD COLUMN     "is_delivery_required" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_pickup_required" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "origin_address" TEXT,
ADD COLUMN     "origin_city" TEXT,
ADD COLUMN     "origin_country" TEXT,
ADD COLUMN     "origin_zipcode" TEXT,
ADD COLUMN     "text" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL,
ALTER COLUMN "service" DROP NOT NULL;

-- AlterTable
ALTER TABLE "user_request_counters" DROP COLUMN "userId",
ADD COLUMN     "user_id" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "request_packages" (
    "id" TEXT NOT NULL,
    "request_id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "length" INTEGER NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "weight" DOUBLE PRECISION NOT NULL,
    "type" "RequestPackageType",
    "is_stackable" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "request_packages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_request_counters_user_id_key" ON "user_request_counters"("user_id");

-- AddForeignKey
ALTER TABLE "requests" ADD CONSTRAINT "requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "request_packages" ADD CONSTRAINT "request_packages_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
