import { useState } from "react";
import { IconButton } from "@mui/material";
import DoneIcon from "@mui/icons-material/Done";
import { Icon } from "../assets";

type Props = {
  text: string;
  disabled?: boolean;
};

export const CopyButton = ({ text, disabled }: Props) => {
  const [copied, setCopied] = useState(false);

  const handleCopyClick = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);

      setTimeout(() => {
        setCopied(false);
      }, 1000);
    } catch {
      setCopied(false);
    }
  };

  return (
    <div
      style={{
        position: "relative",
      }}
    >
      <IconButton
        disableRipple
        color="success"
        size="small"
        sx={{
          display: copied ? "block" : "none",
          padding: "4px 3px 0 5px",
          opacity: copied ? 1 : 0,
          transition: "opacity 0.3s ease",
        }}
      >
        <DoneIcon />
      </IconButton>

      <IconButton
        onClick={handleCopyClick}
        size="small"
        disabled={disabled}
        sx={{
          opacity: disabled ? 0.6 : copied ? 0 : 1,
          padding: "4px 3px 0 5px",
          display: copied ? "none" : "block",
          transition: "opacity 0.3s ease",
        }}
      >
        <Icon.Copy />
      </IconButton>
    </div>
  );
};
