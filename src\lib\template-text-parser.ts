export type ParsedPart =
  | { type: "text"; value: string }
  | { type: "token"; value: string };

/**
 * Splits a string into plain text parts and first-level `{ ... }` token parts.
 *
 * - Top-level `{ ... }` blocks become `type: "token"` with *inner* content (without braces).
 * - Outside content is returned as `type: "text"`.
 * - <PERSON>les nested braces, strings (`"..."`) with escaped quotes, and braces inside strings.
 */
export function parseTemplateText(input: string): ParsedPart[] {
  const parts: ParsedPart[] = [];
  let buffer = "";
  let depth = 0;
  let inString = false;
  let escape = false;
  let tokenBuffer = "";

  for (let i = 0; i < input.length; i++) {
    const ch = input[i];

    if (inString) {
      // Handle escape in string
      if (escape) {
        if (depth > 0) tokenBuffer += "\\" + ch;
        else buffer += "\\" + ch;
        escape = false;
        continue;
      }
      if (ch === "\\") {
        escape = true;
        continue;
      }
      if (ch === '"') {
        inString = false;
      }
      if (depth > 0) tokenBuffer += ch;
      else buffer += ch;
      continue;
    }

    // Outside string mode
    if (ch === '"') {
      inString = true;
      if (depth > 0) tokenBuffer += ch;
      else buffer += ch;
      continue;
    }

    if (ch === "{") {
      if (depth === 0) {
        // Starting a new token
        if (buffer) {
          parts.push({ type: "text", value: buffer });
          buffer = "";
        }
        tokenBuffer = "";
      } else {
        tokenBuffer += ch;
      }
      depth++;
      continue;
    }

    if (ch === "}") {
      depth--;
      if (depth === 0) {
        // End of a token
        parts.push({ type: "token", value: tokenBuffer });
        tokenBuffer = "";
      } else if (depth > 0) {
        tokenBuffer += ch;
      } else {
        throw new SyntaxError(`Unmatched closing brace at position ${i}`);
      }
      continue;
    }

    // Normal char
    if (depth > 0) tokenBuffer += ch;
    else buffer += ch;
  }

  if (inString) {
    throw new SyntaxError("Unterminated string literal in input");
  }
  if (depth !== 0) {
    throw new SyntaxError("Unmatched opening brace in input");
  }

  if (buffer) {
    parts.push({ type: "text", value: buffer });
  }

  return parts;
}
