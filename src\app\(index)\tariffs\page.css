/* Screen 1 - tariff - open */

position: relative;
width: 1440px;
height: 1003px;

background: #FFFFFF;
box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);


/* Rectangle 50 */

box-sizing: border-box;

position: absolute;
width: 461px;
height: 176px;
left: 216px;
top: 543px;

border: 1px solid #EFEFEF;
filter: drop-shadow(2px 2px 3px rgba(198, 197, 202, 0.55));
border-radius: 10px;


/* Rectangle 51 */

box-sizing: border-box;

position: absolute;
width: 741px;
height: 176px;
left: 683px;
top: 543px;

border: 1px solid #EFEFEF;
filter: drop-shadow(2px 2px 3px rgba(198, 197, 202, 0.55));
border-radius: 10px;


/* Rectangle 49 */

position: absolute;
width: 1196px;
height: 198px;
left: 228px;
top: 539px;



/* Form_for_text */

position: absolute;
width: 596px;
height: 162px;
left: 830px;
top: 169px;



/* window_text */

box-sizing: border-box;

position: absolute;
width: 596px;
height: 122px;
left: 830px;
top: 209px;

background: #F5F5F5;
border: 1px solid #F5F5F5;
border-radius: 10px;


/* text */

position: absolute;
width: 558px;
height: 85px;
left: 853px;
top: 223px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 140%;
/* or 17px */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* window_text */

box-sizing: border-box;

position: absolute;
width: 596px;
height: 34px;
left: 830px;
top: 169px;

background: #F5F5F5;
border: 1px solid #F5F5F5;
border-radius: 10px;


/* text */

position: absolute;
width: 562.89px;
height: 15px;
left: 854px;
top: 177px;

font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* copy-and-paste */

position: absolute;
width: 25.63px;
height: 24px;
left: 1391px;
top: 305px;

border-radius: 10px;


/* Rectangle */

position: absolute;
left: 25%;
right: 25%;
top: 20.83%;
bottom: 20.83%;

/* Black */
border: 1px solid #1B1D1F;
border-radius: 0.5px;


/* Path  */

position: absolute;
left: 16.67%;
right: 33.33%;
top: 12.5%;
bottom: 29.17%;

/* Black */
border: 1px solid #1B1D1F;
border-radius: 0.5px;


/* copy-and-paste */

position: absolute;
width: 25.63px;
height: 24px;
left: 1391px;
top: 178px;

border-radius: 10px;


/* Rectangle */

position: absolute;
left: 25%;
right: 25%;
top: 20.83%;
bottom: 20.83%;

/* Black */
border: 1px solid #1B1D1F;
border-radius: 0.5px;


/* Path  */

position: absolute;
left: 16.67%;
right: 33.33%;
top: 12.5%;
bottom: 29.17%;

/* Black */
border: 1px solid #1B1D1F;
border-radius: 0.5px;


/* Rectangle 5 */

position: absolute;
width: 191px;
height: 1003px;
left: 0px;
top: 0px;

background: #70B57D;


/* Label(Replace) */

position: absolute;
visibility: hidden;
width: 425px;
height: 86px;
left: 854px;
top: 157px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* Logo */

position: absolute;
width: 119px;
height: 28.12px;
left: calc(50% - 119px/2 - 637.5px);
top: 39px;



/* logo_text */

position: absolute;
width: 81.66px;
height: 12.74px;
left: 60.34px;
top: 46.6px;



/* Vector */

position: absolute;
left: 6.63%;
right: 93.18%;
top: 4.65%;
bottom: 94.1%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 4.74%;
right: 94.38%;
top: 4.65%;
bottom: 94.08%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 5.67%;
right: 93.46%;
top: 4.65%;
bottom: 94.09%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 6.89%;
right: 92.46%;
top: 4.65%;
bottom: 94.09%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 8.31%;
right: 90.8%;
top: 4.65%;
bottom: 94.08%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 4.19%;
right: 95.25%;
top: 4.67%;
bottom: 94.1%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 7.63%;
right: 91.71%;
top: 4.67%;
bottom: 94.1%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 9.18%;
right: 90.14%;
top: 4.67%;
bottom: 94.1%;

background: #FFFFFF;


/* logo_circle */

position: absolute;
width: 28.12px;
height: 28.12px;
left: 23px;
top: 39px;



/* Ellipse 1 */

position: absolute;
width: 28.12px;
height: 28.12px;
left: 23px;
top: 39px;

background: #FFFFFF;


/* letters */

position: absolute;
width: 11.76px;
height: 14.1px;
left: 31.15px;
top: 45.79px;



/* Vector */

position: absolute;
left: 2.58%;
right: 97.02%;
top: 4.57%;
bottom: 94.77%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.2%;
right: 97.47%;
top: 4.58%;
bottom: 94.78%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.16%;
right: 97.45%;
top: 5.3%;
bottom: 94.03%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.62%;
right: 97.06%;
top: 5.32%;
bottom: 94.04%;

background: #1A1A1A;


/* Menu */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 6px;

position: absolute;
width: 189px;
height: 194px;
left: 0px;
top: 126px;



/* menu-item_act_home */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 189px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* file */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Path  */

position: absolute;
left: 54.17%;
right: 20.83%;
top: 12.5%;
bottom: 62.5%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Rectangle  */

position: absolute;
left: 20.83%;
right: 4.17%;
top: 12.5%;
bottom: 29.17%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;
transform: rotate(90deg);


/* text */

width: 152px;
height: 16px;

font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_requests */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

background: #568D60;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* folder */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_dashboard */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* column-chart */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Path */

position: absolute;
left: 12.5%;
right: 70.83%;
top: 54.17%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Path */

position: absolute;
left: 41.67%;
right: 41.67%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Path */

position: absolute;
left: 70.83%;
right: 12.5%;
top: 33.33%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_orders */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* settings */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.35%;
right: 12.35%;
top: 9.54%;
bottom: 9.54%;

border: 1px solid #FFFFFF;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* align-right */

position: absolute;
width: 30px;
height: 30px;
left: calc(50% - 30px/2 - 550px);
top: calc(50% - 30px/2 - 447.5px);



/* Path  */

position: absolute;
left: 45.83%;
right: 20.83%;
top: 45.83%;
bottom: 45.83%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Path  */

position: absolute;
left: 20.83%;
right: 20.83%;
top: 25%;
bottom: 66.67%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Path  */

position: absolute;
left: 29.17%;
right: 20.83%;
top: 66.67%;
bottom: 25%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Frame 106 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 148px;

position: absolute;
width: 557px;
height: 16px;
left: 216px;
top: 341px;



/* button paste */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 53px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* log-in */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;



/* paste */

width: 33px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #70B57D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Group 6 */

position: absolute;
width: 625px;
height: 223px;
left: 216px;
top: 108px;



/* Form_for_text */

position: absolute;
width: 596px;
height: 223px;
left: 216px;
top: 108px;



/* Label(Replace) */

position: absolute;
width: 440.71px;
height: 14.28px;
left: 219.06px;
top: 108px;

font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 16px;
line-height: 16px;
/* or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* text */

position: absolute;
width: 548px;
height: 158px;
left: 240px;
top: 144px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 140%;
/* or 17px */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* window_text */

box-sizing: border-box;

position: absolute;
width: 596px;
height: 202px;
left: 216px;
top: 129px;

border: 1px solid #C6C5CA;
border-radius: 10px;


/* close */

position: absolute;
width: 21px;
height: 21.42px;
left: 783.26px;
top: 133.88px;

border-radius: 10px;


/* Vector */

position: absolute;
left: 26.43%;
right: 26.43%;
top: 26.43%;
bottom: 26.43%;

/* Black */
border: 1px solid #1B1D1F;


/*  button_extract */

position: absolute;
width: 43px;
height: 43px;
left: 798px;
top: 198px;

filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));


/* Ellipse 2 */

position: absolute;
width: 43px;
height: 43px;
left: 798px;
top: 198px;

background: #70B57D;


/* send */

position: absolute;
width: 24px;
height: 24px;
left: 808px;
top: 208px;



/* Vector */

position: absolute;
left: 25.21%;
right: 28.38%;
top: 17.59%;
bottom: 35.99%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;
transform: matrix(0.72, 0.7, -0.72, 0.7, 0, 0);


/* Vector */

position: absolute;
left: -29.17%;
right: 44.77%;
top: -8.93%;
bottom: 24.53%;

border: 1px solid #FFFFFF;
transform: matrix(0.72, 0.7, -0.72, 0.7, 0, 0);


/* Submit */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;
isolation: isolate;

position: absolute;
width: 186px;
height: 40px;
left: 1188px;
top: 35px;

background: #70B57D;


/* person

account, face, human, people, person, profile, user
*/

display: none;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* plus */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;


/* Text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 6px;
gap: 10px;

width: 126px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Create new request */

width: 114px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Badge */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 6px;
gap: 10px;

display: none;
width: 47px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* 99+ */

width: 34px;
height: 24px;

font-family: 'DM Sans';
font-style: normal;
font-weight: 400;
font-size: 18px;
line-height: 24px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;

/* Community/White */
color: #FFFFFF;

opacity: 0.6;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* notification */

position: absolute;
display: none;
width: 12px;
height: 12px;
right: -4px;
top: -4px;

background: #FF0000;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* dropdown_selecr */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

position: absolute;
visibility: hidden;
width: 188px;
height: 168px;
left: 1236px;
top: 777px;

background: #FFFFFF;
border: 1px solid #C6C5CA;
border-radius: 0px;


/* dropdown_selecr */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

position: absolute;
visibility: hidden;
width: 188px;
height: 168px;
left: 1032px;
top: 777px;

background: #FFFFFF;
border: 1px solid #C6C5CA;
border-radius: 0px;


/* dropdown_selecr */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

position: absolute;
visibility: hidden;
width: 188px;
height: 168px;
left: 930px;
top: 674px;

background: #FFFFFF;
border: 1px solid #C6C5CA;
border-radius: 0px;


/* Help_center */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 28px;
gap: 5px;

position: absolute;
width: 159px;
height: 52px;
left: 20px;
top: 800px;

border: 1px solid #E6EFFE;


/* chat */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Shape */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

border: 1px solid #FFFFFF;


/* Help us improve – leave a review. */

width: 110px;
height: 32px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Group 3 */

position: absolute;
width: 1208px;
height: 66px;
left: 216px;
top: 410px;



/* Rectangle 44 */

position: absolute;
width: 1208px;
height: 66px;
left: 216px;
top: 410px;

background: #70B57D;
border-radius: 8px 8px 0px 0px;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

position: absolute;
width: 1068px;
height: 63px;
left: 228px;
top: 412px;



/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 1068px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Frame 580678476 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 80px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Transport */

width: 61px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* filter_list */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* ico-/-24-/-ui-/-filter_list */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Icon-color */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 20.83%;
bottom: 20.83%;

background: #FFFFFF;


/* bounding_box:c2all */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Frame 580678477 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 80px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Agent */

width: 47px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* filter_list */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* ico-/-24-/-ui-/-filter_list */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Icon-color */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 20.83%;
bottom: 20.83%;

background: #FFFFFF;


/* bounding_box:c2all */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Direction */

width: 168px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Carrier */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Transit time */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Total */

width: 100px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Margin */

width: 100px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* Selling rate */

width: 100px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Profit */

width: 100px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Group 7 */

position: absolute;
width: 442px;
height: 42px;
left: 216px;
top: 32px;



/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

position: absolute;
width: 442px;
height: 22px;
left: 216px;
top: 32px;



/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 442px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* request № */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* itinerary */

width: 125px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* piece */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* weight */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* volume */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* chargeable weight */

width: 120px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

position: absolute;
width: 442px;
height: 20px;
left: 216px;
top: 54px;



/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 442px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* 000000 */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Moscow - Belgrade */

width: 125px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* 000000 */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* 000000 */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 000000 */

width: 80px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0000000 */

width: 120px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector 3 */

position: absolute;
width: 625.02px;
height: 0px;
left: 215.99px;
top: 77.75px;

border: 1px solid #C6C5CA;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
isolation: isolate;

position: absolute;
width: 596px;
height: 34px;
left: 830px;
top: 129px;

background: #FFFFFF;
/* grey */
border: 1px solid #C6C5CA;
border-radius: 10px;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 16px;

width: 556px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 524px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 524px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
width: 52px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 36px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* attach */

position: absolute;
width: 24px;
height: 24px;
left: calc(50% - 24px/2 + 66px);
top: calc(50% - 24px/2 - 191.5px);



/* vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 16.67%;
bottom: 16.67%;

/* Black */
border: 1px solid #1B1D1F;


/* account_circle */

position: absolute;
width: 40px;
height: 40px;
left: 1386px;
top: 35px;



/* Ellipse 1 */

position: absolute;
width: 40px;
height: 40px;
left: 0px;
top: 0px;

background: #70B57D;


/* user_big_outlined */

position: absolute;
width: 20px;
height: 20px;
left: 10px;
top: 10px;



/* ico-/-24-/-user-/-user_big_outlined */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Icon-color */

position: absolute;
left: 8.35%;
right: 8.32%;
top: 8.33%;
bottom: 8.33%;

background: #FFFFFF;


/* bounding_box:c2all */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Frame ********* */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-end;
padding: 0px;
gap: 12px;

position: absolute;
width: 1196px;
height: 64px;
left: 228px;
top: 475px;



/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 1068px;
height: 63px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 1068px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* AIR */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Company */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Belgrade - San Fransisco */

width: 168px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Carrier 1 */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 8 days */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Frame 580678455 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame ********* */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* % */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
text-align: right;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678456 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678457 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

width: 116px;
height: 64px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* star */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* star */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 12.42%;

background: #70B57D;
border: 1px solid #70B57D;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 25%;
right: 50%;
top: 37.5%;
bottom: 12.5%;

border: 1px solid #1A1A1A;
transform: rotate(-90deg);


/* Frame 580678475 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

position: absolute;
width: 1196px;
height: 192px;
left: 228px;
top: 736px;



/* Frame ********* */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-end;
padding: 0px;
gap: 12px;

width: 1196px;
height: 64px;

border-bottom: 1px solid #C6C5CA;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 1068px;
height: 63px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 1068px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* AIR */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Company */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Belgrade - San Fransisco */

width: 168px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Carrier 1 */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 8 days */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Frame 580678455 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame ********* */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* % */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
text-align: right;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678456 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678457 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

width: 116px;
height: 64px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* star */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* star */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 12.42%;

background: #70B57D;
border: 1px solid #70B57D;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 25%;
right: 50%;
top: 37.5%;
bottom: 12.5%;

border: 1px solid #1A1A1A;
transform: rotate(-90deg);


/* Frame 580678471 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-end;
padding: 0px;
gap: 12px;

width: 1196px;
height: 64px;

border-bottom: 1px solid #C6C5CA;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 1068px;
height: 63px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 1068px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* AIR */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Company */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Belgrade - San Fransisco */

width: 168px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Carrier 1 */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 8 days */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Frame 580678455 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame ********* */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* % */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
text-align: right;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678456 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678457 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

width: 116px;
height: 64px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* star */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* star */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 12.42%;

background: #70B57D;
border: 1px solid #70B57D;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 25%;
right: 50%;
top: 37.5%;
bottom: 12.5%;

border: 1px solid #1A1A1A;
transform: rotate(-90deg);


/* Frame 580678472 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-end;
padding: 0px;
gap: 12px;

width: 1196px;
height: 64px;

border-bottom: 1px solid #C6C5CA;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

width: 1068px;
height: 63px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 1068px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* AIR */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Company */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Belgrade - San Fransisco */

width: 168px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Carrier 1 */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 8 days */

width: 80px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Frame 580678455 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame ********* */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* % */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
text-align: right;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678456 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* Frame 580678457 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 48px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* 000000 */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* USD */

width: 26px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 16px 0px 0px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Right Icon Settings */

width: 20px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 22.05%;
right: 42.05%;
top: 38.35%;
bottom: 42.51%;

/* black */
background: #1A1A1A;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

width: 116px;
height: 64px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* star */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* star */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 12.42%;

background: #70B57D;
border: 1px solid #70B57D;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 25%;
right: 50%;
top: 37.5%;
bottom: 12.5%;

border: 1px solid #1A1A1A;
transform: rotate(-90deg);


/* Frame 580678481 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

position: absolute;
width: 455px;
height: 161px;
left: 228px;
top: 546px;



/* Frame 580678467 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 455px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 580678459 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 140px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Type */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678461 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 70px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Unit */

width: 76px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Rate */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 580678462 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 100px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Total */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678478 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Frame 580678481 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Frame 580678482 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 440px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Label(Replace) */

width: 455px;
height: 25px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: flex-end;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Frame 580678482 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

position: absolute;
width: 723px;
height: 169px;
left: 695px;
top: 546px;



/* Frame 580678467 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 723px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 580678459 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 140px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Type */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678461 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 70px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Unit */

width: 76px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Rate */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 580678462 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 126px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Total */

width: 58px;
height: 48px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Margin */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Rate */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Profit */

width: 126px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* grey */
color: #C6C5CA;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* Frame 580678478 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 741px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678485 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 30px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 22px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 21px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 24px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 8px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input-checkbox */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

width: 16px;
height: 16px;

background: #EFEFEF;
border: 1px solid #70B57D;

/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* symbol/classic */

width: 20px;
height: 20px;

opacity: 0;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Bounding box */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D9D9D9;


/* input_check_animation */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Bounding box */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D9D9D9;


/* Line 2 */

box-sizing: border-box;

position: absolute;
left: 24.14%;
right: 50.91%;
top: 52.07%;
bottom: 47.93%;

border: 1.75px solid #70B57D;
transform: rotate(45deg);


/* Line 3 */

box-sizing: border-box;

position: absolute;
left: 41.75%;
right: 9.8%;
top: 35.44%;
bottom: 64.56%;

border: 1.75px solid #70B57D;
transform: rotate(-45deg);


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Frame 580678483 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 741px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678485 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 30px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 22px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 21px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 24px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 8px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input-checkbox */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

width: 16px;
height: 16px;

background: #70B57D;
border: 1px solid #70B57D;

/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* symbol/classic */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Bounding box */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D9D9D9;


/* input_check_animation */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Bounding box */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D9D9D9;


/* Line 2 */

box-sizing: border-box;

position: absolute;
left: 24.14%;
right: 50.91%;
top: 52.07%;
bottom: 47.93%;

border: 1.75px solid #FFFFFF;
transform: rotate(45deg);


/* Line 3 */

box-sizing: border-box;

position: absolute;
left: 41.75%;
right: 9.8%;
top: 35.44%;
bottom: 64.56%;

border: 1.75px solid #FFFFFF;
transform: rotate(-45deg);


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Vector 15 */

width: 721px;
height: 0px;

border: 1px solid #70B57D;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 580678485 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 741px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 140px;
height: 24px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 128px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 124px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Placeholder(Replace) */

width: 124px;
height: 24px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 70px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 76px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(255, 255, 255, 0.98);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

background: rgba(255, 255, 255, 0.98);


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

border: 1px solid rgba(255, 255, 255, 0.97);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(255, 255, 255, 0.97);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678485 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 30px;
height: 24px;

background: #FFFFFF;
border: 1px solid rgba(255, 255, 255, 0.98);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 22px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 21px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(255, 255, 255, 0.99);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 24px;
height: 24px;

background: #FFFFFF;
border: 1px solid rgba(255, 255, 255, 0.98);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 16px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 8px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(255, 255, 255, 0.99);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

width: 52px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 44px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* input_service */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px 4px;
isolation: isolate;

width: 60px;
height: 24px;

background: #FFFFFF;
/* Grey very light BG

F7F7F7
*/
border: 1px solid #D9D9D9;

/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* IconLeft(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 0px 0px 16px;

margin: 0 auto;
display: none;
width: 40px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Left Icon Settings

filter, find, glass, look, magnify, magnifying, search, see
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 14.48%;
right: 14.48%;
top: 14.46%;
bottom: 14.46%;

background: #1D1D24;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 0px 2px;

margin: 0 auto;
width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Placeholder(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 36px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Placeholder(Replace) */

width: 36px;
height: 24px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* IconRight(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 16px 0px 0px;

margin: 0 auto;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Right Icon Settings

app, application, arrow, components, direction, down, drop, interface, navigation, screen, site, ui, ux, web, website
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* FormSymbol */

position: absolute;
left: 35.04%;
right: 35.04%;
top: 40.43%;
bottom: 40.43%;

/* black */
background: #1A1A1A;


/* Button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;

margin: 0 auto;
display: none;
width: 56px;
height: 56px;

background: #E6EFFE;
border-radius: 0px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* Label(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 8px;

position: absolute;
display: none;
width: 49px;
height: 16px;
left: 8px;
top: -8px;

background: #FFFFFF;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Label(Replace) */

width: 45px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Description(Show/Hide) */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
display: none;
width: 67px;
height: 16px;
left: 0px;
bottom: -24px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;
z-index: 5;


/* Description(Rename) */

width: 67px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #83828D;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* cancel */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* Vector */

position: absolute;
visibility: hidden;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #EB4E3D;
border: 1px solid #FFFFFF;


/* Label(Replace) */

width: 723px;
height: 25px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: flex-end;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* Account_menu */

position: absolute;
width: 236px;
height: 40px;
left: 1190px;
top: 78px;



/* menu_bg */

box-sizing: border-box;

position: absolute;
width: 236px;
height: 40px;
left: 1190px;
top: 78px;

background: #FFFFFF;
border: 1px solid #F0F0F0;
box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);


/* Login_menu */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

position: absolute;
width: 191.5px;
height: 24px;
left: 1214px;
top: 86px;



/* login_menu_my_acc */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

display: none;
width: 102px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector 1 */

display: none;
width: 191.5px;
height: 0px;

border: 1px solid #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* login_menu_settings */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

display: none;
width: 82px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* settings */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.35%;
right: 12.35%;
top: 9.54%;
bottom: 9.54%;

/* Black */
border: 1px solid #1B1D1F;


/* settings */

width: 48px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #000000;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector 2 */

display: none;
width: 191.5px;
height: 0px;

border: 1px solid #EFEFEF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* login_menu_logout */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

width: 75px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* log-in */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* log out */

width: 41px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #000000;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678486 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 1px;
gap: 1px;

position: absolute;
width: 55px;
height: 55px;
left: 1369px;
top: 935px;

background: #D9D9D9;
box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
border-radius: 1000px;


/* share-android */

width: 29px;
height: 29px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.25%;
right: 16.25%;
top: 12.5%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* Rectangle 52 */

box-sizing: border-box;

position: absolute;
width: 294px;
height: 80px;
left: 1064px;
top: 910px;

background: #FFFFFF;
border: 1px solid #EEEEEE;
box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
border-radius: 10px;


/* This button is for integration with external systems. Please contact us to activate this feature. */

position: absolute;
width: 265px;
height: 48px;
left: 1085px;
top: 928px;

/* ys_small_medium */
font-family: 'YS Text';
font-style: normal;
font-weight: 500;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #000000;