import type { PrismaClient } from "@prisma/client";

import { Injectable } from "../utils";
import { ForbiddenSignalError, NotFoundSignalError } from "../error/http";

export * from "./controller";

export class RequestTemplateService extends Injectable {
    private readonly prisma!: PrismaClient;

    async getForUser(dto: {
        userId: string;
    }) {
        return await this.prisma.userRequestTemplate.findMany({
            where: {
                userId: dto.userId,
                deletedAt: null,
            },
        });
    }

    async create(dto: {
        userId: string;

        name: string;

        title: string;
        content: string;
    }) {
        return await this.prisma.userRequestTemplate.create({
            data: dto,
        });
    }

    async update({
        userId,
        id,
        ...dto
    }: {
        userId: string;

        id: string;

        name?: string;

        title?: string;
        content?: string;
    }) {
        const requestTemplate = await this.prisma.userRequestTemplate.findUnique({
            where: {
                id,
            },
        });

        if (!requestTemplate) {
            throw new NotFoundSignalError("request template");
        }

        if (requestTemplate.userId !== userId) {
            throw new ForbiddenSignalError("not author");
        }

        return await this.prisma.userRequestTemplate.update({
            where: {
                id,
            },
            data: dto,
        });
    }

    async delete(dto: {
        userId: string;

        id: string;
    }) {
        const requestTemplate = await this.prisma.userRequestTemplate.findUnique({
            where: {
                id: dto.id,
            },
        });

        if (!requestTemplate) {
            throw new NotFoundSignalError("request template");
        }

        if (requestTemplate.userId !== dto.userId) {
            throw new ForbiddenSignalError("not author");
        }

        return await this.prisma.userRequestTemplate.update({
            where: {
                id: dto.id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
    }
}
