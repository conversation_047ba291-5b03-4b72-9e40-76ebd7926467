import { useState } from "react";
import { Main } from "./components";
import { Sidebar } from "./components";
import { CreateRequestPage } from "./features/create-requests/CreateRequestPage";

function App() {
  const [open, setOpen] = useState(true);
  const handleSidebarToggle = (state: boolean) => {
    setOpen(state)
  }
  return (
    <>
      <Sidebar handleSidebarClose={handleSidebarToggle} open={open} />
      <Main open={open}>
        <CreateRequestPage
          handleSidebarOpen={handleSidebarToggle}
          sidebarOpen={open}
        />
      </Main>
    </>
  );
}

export default App;
