.addressesInfoContainer {
    display: flex;
    justify-content: center;
    gap: 16px;

    width: 100%;

    flex-shrink: 1;
    flex-grow: 1;
}

.additionalDetailsContainer {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
}

.inputsContainer {
  display: grid;
  grid-template-columns: repeat(6, minmax(0, 1fr));
  gap: 16px;
}

.inputsContainer > div:nth-child(1) {
  grid-column: span 2;
}

.deliveryInfoContainer {
  border: 1px solid #C6C5CA;
  flex-basis: 392px;
  border-radius: 10px;
  padding: 0 27px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-shrink: 1;
  gap: 16px;
  flex-grow: 1;
}

.deliveryInfoContainer > div {
  display: flex;
  gap: 16px;
}
