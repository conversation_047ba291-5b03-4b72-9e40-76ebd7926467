import { SvgIcon } from "@mui/material";

export const Close = () => (
  <svg
    width="22"
    height="23"
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.7078 16.6354L5.80835 6.53931M15.7078 6.53931L5.80835 16.6354"
      stroke="#1B1D1F"
      strokeLinecap="round"
    />
  </svg>
);

export const Copy = () => (
  <SvgIcon>
    <svg
      width="26"
      height="24"
      viewBox="0 0 26 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="6.40869"
        y="5"
        width="12.8172"
        height="14"
        rx="0.5"
        stroke="#1B1D1F"
        strokeLinejoin="round"
      />
      <path
        d="M4.27246 17C4.27246 17 4.27246 6.09961 4.27246 3.49383C4.27246 3.21768 4.49632 3 4.77246 3H17.0897"
        stroke="#1B1D1F"
        strokeLinecap="round"
      />
    </svg>
  </SvgIcon>
);

export const Lock = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3.75"
      y="8.25"
      width="10.5"
      height="7.5"
      rx="0.5"
      stroke="#C6C5CA"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.25 6C5.25 3.92893 6.92893 2.25 9 2.25V2.25C11.0711 2.25 12.75 3.92893 12.75 6V8.25H5.25V6Z"
      stroke="#C6C5CA"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Mail = () => (
  <svg
    width="26"
    height="24"
    viewBox="0 0 26 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="2.17944"
      y="5"
      width="21.362"
      height="14"
      rx="0.5"
      stroke="#1B1D1F"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.17944 5L12.5383 13.7285C12.7244 13.8854 12.9965 13.8854 13.1826 13.7285L23.5415 5"
      stroke="#1B1D1F"
    />
    <path d="M2.17944 19L9.65615 11" stroke="#1B1D1F" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.5414 19L16.0647 11L23.5414 19Z"
      stroke="#1B1D1F"
    />
  </svg>
);

export const OpenSidebar = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23.75 15H13.75" stroke="#1B1D1F" strokeLinecap="round" />
    <path d="M23.75 8.75H6.25" stroke="#1B1D1F" strokeLinecap="round" />
    <path d="M23.75 21.25H8.75" stroke="#1B1D1F" strokeLinecap="round" />
  </svg>
);

export const Paste = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.6667 2H12.8889C13.1836 2 13.4662 2.14048 13.6746 2.39052C13.8829 2.64057 14 2.97971 14 3.33333V12.6667C14 13.0203 13.8829 13.3594 13.6746 13.6095C13.4662 13.8595 13.1836 14 12.8889 14H10.6667M6.66667 11.3333L10 8M10 8L6.66667 4.66667M10 8H2"
      stroke="#70B57D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Plus = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20 12H4M12 4V20" stroke="white" strokeLinecap="round" />
  </svg>
);

export const Send = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_390_7314)">
      <path
        d="M21.9999 12.0001H6.04993"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.9999 12L3.57544 20.6522C3.16236 20.8462 2.71879 20.4475 2.88337 20.0301L5.98011 12.1773C6.0251 12.0632 6.0251 11.9369 5.98011 11.8228L2.88337 3.96996C2.71879 3.55262 3.16236 3.15391 3.57544 3.34789L21.9999 12Z"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_390_7314">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Trash = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 11L10 18M14 11V18M19 8L18 20.25C18 20.7141 17.7893 21.1592 17.4142 21.4874C17.0391 21.8156 16.5304 22 16 22H8C7.46957 22 6.96086 21.8156 6.58579 21.4874C6.21071 21.1592 6 20.7141 6 20.25L5 8H19ZM9 4V3C9 2.44772 9.44772 2 10 2H14C14.5523 2 15 2.44772 15 3V4C15 4.55228 14.5523 5 14 5H10C9.44772 5 9 4.55228 9 4ZM4 7.5V5.5C4 5.22386 4.22386 5 4.5 5H19.5C19.7761 5 20 5.22386 20 5.5V7.5C20 7.77614 19.7761 8 19.5 8H4.5C4.22386 8 4 7.77614 4 7.5Z"
      stroke="#EB4E3D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Logout = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6 14H3C2.73478 14 2.48043 13.8946 2.29289 13.7071C2.10536 13.5196 2 13.2652 2 13V3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H6M11 11L14 8L11 5M14 8H6"
      stroke="#EB4E3D"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Profile = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1179_32990)">
      <circle cx="20" cy="20" r="20" fill="#70B57D" />
      <g clipPath="url(#clip1_1179_32990)">
        <path d="M20.0023 20.0003C22.3042 20.0003 24.1702 18.1348 24.1702 15.8337C24.1702 13.5325 22.3042 11.667 20.0023 11.667C17.7004 11.667 15.8344 13.5325 15.8344 15.8337C15.8344 18.1348 17.7004 20.0003 20.0023 20.0003ZM20.0023 13.3337C21.3834 13.3337 22.503 14.4529 22.503 15.8337C22.503 17.2144 21.3834 18.3337 20.0023 18.3337C18.6211 18.3337 17.5015 17.2144 17.5015 15.8337C17.5015 14.4529 18.6211 13.3337 20.0023 13.3337ZM28.2464 27.1253L26.671 23.967C25.9643 22.5572 24.5222 21.667 22.9448 21.667H17.0597C15.4824 21.667 14.0402 22.5572 13.3336 23.967L11.7581 27.1253C11.6281 27.3833 11.641 27.6901 11.7921 27.9363C11.9432 28.1824 12.2111 28.3328 12.5 28.3337H27.5045C27.7935 28.3328 28.0613 28.1824 28.2125 27.9363C28.3636 27.6901 28.3765 27.3833 28.2464 27.1253ZM13.8504 26.667L14.8257 24.717C15.2485 23.8704 16.1132 23.335 17.0597 23.3337H22.9448C23.8914 23.335 24.7561 23.8704 25.1788 24.717L26.1541 26.667H13.8504Z" fill="white" />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_1179_32990">
        <rect width="40" height="40" fill="white" />
      </clipPath>
      <clipPath id="clip1_1179_32990">
        <rect width="20" height="20" fill="white" transform="translate(10 10)" />
      </clipPath>
    </defs>
  </svg>
);

export const Edit = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.3333 2.00001C11.5085 1.82491 11.7163 1.68606 11.9451 1.59129C12.1738 1.49653 12.4191 1.44775 12.6667 1.44775C12.9142 1.44775 13.1595 1.49653 13.3882 1.59129C13.617 1.68606 13.8248 1.82491 14 2.00001C14.1751 2.17511 14.314 2.38293 14.4087 2.61167C14.5035 2.84041 14.5523 3.08569 14.5523 3.33334C14.5523 3.58099 14.5035 3.82627 14.4087 4.05501C14.314 4.28375 14.1751 4.49157 14 4.66668L5.00001 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00001Z"
      stroke="#467c8d"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Upload = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 10V12.6667C14 13.0203 13.8829 13.3594 13.6746 13.6095C13.4662 13.8595 13.1836 14 12.8889 14H3.11111C2.81643 14 2.53381 13.8595 2.32544 13.6095C2.11706 13.3594 2 13.0203 2 12.6667V10M11.3333 5.33333L8 2M8 2L4.66667 5.33333M8 2V10"
      stroke="#70B57D"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
