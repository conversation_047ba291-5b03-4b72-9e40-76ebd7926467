import type { TreeNode } from "./template-tree-parser";

function getIsTruthy(value: string | null): boolean {
  return (!!value || value === "true") && value !== "false" && value !== "0";
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function evaluateTree(data: any, tree: TreeNode): string | null {
  switch (tree.type) {
    case "value": {
      return tree.value;
    }

    case "reference": {
      if (tree.name in data) {
        return data[tree.name];
      }

      return null;
    }

    case "ternary": {
      const conditionValue = evaluateTree(data, tree.condition);

      const isTruthy = getIsTruthy(conditionValue);

      return isTruthy
        ? evaluateTree(data, tree.true)
        : evaluateTree(data, tree.false);
    }

    case "nullish": {
      for (const node of tree.values) {
        const value = evaluateTree(data, node);

        if (value) {
          return value;
        }
      }

      return null;
    }

    case "list": {
      const values = tree.values.map((node) => evaluateTree(data, node));

      if (values.every((v) => getIsTruthy(v))) {
        return values.join("");
      }

      return null;
    }

    case "boolean": {
      switch (tree.operator) {
        case "AND": {
          for (let i = 0; i < tree.operands.length; i++) {
            const operand = tree.operands[i]!;

            const value = evaluateTree(data, operand);
            const isTruthy = getIsTruthy(value);

            if (!isTruthy) {
              return value;
            }

            if (i === tree.operands.length - 1) {
              return value;
            }
          }

          return null;
        }

        case "OR": {
          for (let i = 0; i < tree.operands.length; i++) {
            const operand = tree.operands[i]!;

            const value = evaluateTree(data, operand);
            const isTruthy = getIsTruthy(value);

            if (isTruthy) {
              return value;
            }

            if (i === tree.operands.length - 1) {
              return value;
            }
          }

          return null;
        }

        default: return null;
      }
    }

    case "plus": {
      const values = tree.operands.map((operand) => String(evaluateTree(data, operand)));

      return values.join("");
    }
  }
}
