import type { Prisma, PrismaClient } from "@prisma/client";

import { Injectable } from "../utils";

const defaultUserRequestTemplate1Content = `
Dear colleagues,

Please provide your quotation asap:

Requested service: {service}
{incoterms} {from.city} - {to.city}
Pickup address: {from.address}
Delivery address: {to.address}

Number / type of packages: {total.quantity} cartons
Dimensions: {packages}
 {package.quantity} pcs., {package.length}*{package.width}*{package.height} cm {package.stackable ? "" : "- NOT stackable"}
{/packages}
Total weight: {total.weight} kgs
Total volume:  {total.volume} cbm
Chargeable weight: {total.chargeableWeight} kgs

Nature of goods: {goods.description}
HS codes: {goods.hsCodes}
Cost of goods: {goods.cost} {goods.currency}
Additional services needed: {services.additional}
Contains dangerous goods: {services.dangerous}


Thank you in advance for your prompt reply!
`.trim();

const defaultUserRequestTemplate2Content = `
Requested service: {service}
{incoterms} {from.city} - {to.city}
Pickup address: {from.address}
Delivery address: {to.address}

Number / type of packages: {total.quantity} cartons
Dimensions: {packages}
 {package.quantity} pcs., {package.length}*{package.width}*{package.height} cm {package.stackable ? "" : "- NOT stackable"}
{/packages}
Total weight: {total.weight} kgs
Total volume:  {total.volume} cbm
Chargeable weight: {total.chargeableWeight} kgs

Nature of goods: {goods.description}
HS codes: {goods.hsCodes}
Cost of goods: {goods.cost} {goods.currency}
Additional services needed: {services.additional}
Contains dangerous goods: {services.dangerous}
`.trim();

const defaultUserRequestTemplate3Content = `
Dear colleagues,

Please provide your quotation asap:

Requested service: {service}
{incoterms} {from.city} - {to.city}
Pickup address: {from.address}
Delivery address: {to.address}

Number / type of packages: {total.quantity} cartons
Dimensions: {packages}
 {package.quantity} pcs., {package.length}*{package.width}*{package.height} cm, {package.weight} kgs, {package.volume} cbm per pcs {package.stackable ? "" : "- NOT stackable"}
{/packages}
Total weight: {total.weight} kgs
Total volume:  {total.volume} cbm
Chargeable weight: {total.chargeableWeight} kgs

Nature of goods: {goods.description}
HS codes: {goods.hsCodes}
Cost of goods: {goods.cost} {goods.currency}
Additional services needed: {services.additional}
Contains dangerous goods: {services.dangerous}

Thank you in advance for your prompt reply!
`.trim();

const DEFAULT_USER_REQUEST_TEMPLATES: Omit<Prisma.UserRequestTemplateCreateManyInput, "userId">[] = [
    {
        name: "Quotation request for partners",
        title: "{ordinal} Quotation {from.city} – {to.city}, {total.weight} kgs, {total.volume} cbm",
        content: defaultUserRequestTemplate1Content,
    },
    {
        name: "Cargo summary",
        title: "{ordinal} {from.city} – {to.city}, {total.weight} kgs, {total.volume} cbm",
        content: defaultUserRequestTemplate2Content,
    },
    {
        name: "Detailed quotation request for partners",
        title: "{ordinal} Quotation {from.city} – {to.city}, {total.weight} kgs, {total.volume} cbm",
        content: defaultUserRequestTemplate3Content,
    },
];

export class UserService extends Injectable {
    private readonly prisma!: PrismaClient;

    async create(dto: {
        email: string,
        hash: string,
    }) {
        const user = await this.prisma.$transaction(async (trx) => {
            const user = await trx.user.create({
                data: {
                    email: dto.email,
                    hash: dto.hash,
                },
            });

            await trx.userRequestTemplate.createMany({
                data: DEFAULT_USER_REQUEST_TEMPLATES.map((template) => ({
                    userId: user.id,
                    name: template.name,
                    title: template.title,
                    content: template.content,
                })),
            });

            return user;
        });

        return user;
    }

    async getById(id: string) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });

        return user;
    }

    async getByEmail(email: string) {
        const user = await this.prisma.user.findUnique({
            where: { email },
        });

        return user;
    }

    async updateHash(dto: {
        email: string;
        hash: string;
    }) {
        await this.prisma.user.update({
            where: {
                email: dto.email,
            },
            data: {
                hash: dto.hash,
            },
        });
    }
}
