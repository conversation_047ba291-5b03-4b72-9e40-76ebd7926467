import { roundUpToTwoDecimals } from "../../../utils/roundUpToTwoDecimals";
import {
  Detail,
  SharedSummaryDetailsKeys,
  ShippingInfoWithEmptyFields,
  Summary,
} from "../types/shippingTypes";

export const getTotalFieldsSum = (
  key: keyof SharedSummaryDetailsKeys,
  details: Detail[] = []
) => {
  if (key === "piece") {
    return details.reduce((sum, item) => sum + (item?.piece ?? 0), 0) ?? 0;
  }

  return details.reduce(
    (sum, item) => sum + (item?.dimension?.[key] ?? 0) * item.piece,
    0
  );
};

export const calculateTotalDensity = (
  volume: number,
  weight: number,
) => {
  if (volume === 0 || weight === 0) {
    return 0;
  }

  return weight / volume;
};

export const getTotalPackageInfo = ({
  details,
  summary,
}: ShippingInfoWithEmptyFields) => {
  const totalDetails = getTotalGoodsInfo(summary);

  const detailedInfo = details?.map(formatPackageDetails).join("\n");
  const dimensionsDetails = !detailedInfo?.length
    ? ""
    : `Dimensions: \n${detailedInfo}`;

  return [totalDetails, dimensionsDetails].filter(Boolean).join("\n");
};

const getTotalGoodsInfo = (summary?: Partial<Summary>) => {
  if (!summary) {
    return "";
  }

  const { volume, weight, piece, density, chargeable_weight } = summary;
  const totalVolume = volume ? `${roundUpToTwoDecimals(volume)}cbm` : "";

  const totalWeight = weight ? `${weight}kgs` : "";

  const totalDensity = density
    ? `Density: ${roundUpToTwoDecimals(density)}`
    : "";
  const chargeableWeight = chargeable_weight
    ? `Chargeable weight: ${roundUpToTwoDecimals(chargeable_weight)}kgs`
    : "";
  const totalNumberOfPackages = piece
    ? piece > 1
      ? `${roundUpToTwoDecimals(piece)} packages`
      : `${roundUpToTwoDecimals(piece)} package`
    : "";
  return [
    totalNumberOfPackages,
    totalVolume,
    totalWeight,
    totalDensity,
    chargeableWeight,
  ]
    .filter(Boolean)
    .join("\n");
};
const formatPackageDetails = (pkg: Partial<Detail | undefined>) => {
  if (!pkg) return "";

  const { piece, dimension, package_type } = pkg;
  const pieces = piece
    ? piece > 1
      ? `${piece} pcs. -`
      : `${piece} pc. -`
    : "";
  const volume = dimension?.volume ? `${roundUpToTwoDecimals(dimension.volume)} cbm` : "";
  const weight = dimension?.weight ? `${roundUpToTwoDecimals(dimension.weight)} kg` : "";
  const dimensions = [
    dimension?.length ? `${roundUpToTwoDecimals(dimension.length)}` : "",
    dimension?.width ? `${roundUpToTwoDecimals(dimension.width)}` : "",
    dimension?.height ? `${roundUpToTwoDecimals(dimension.height)}` : "",
  ]
    .filter(Boolean)
    .join("*");
  const stackable = dimension?.is_stackable ? "not stackable" : "stackable";
  const packageType = package_type
    ? `package type: ${package_type}`
    : "";

  return `${pieces} ${[
    volume,
    weight,
    dimensions ? `${dimensions}` : "",
    stackable,
    packageType,
  ]
    .filter(Boolean)
    .join(", ")}`;
};
