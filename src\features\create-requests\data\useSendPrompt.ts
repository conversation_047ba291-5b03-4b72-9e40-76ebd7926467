import { useRef, useState } from "react";
import { ShippingInfo } from "../types/shippingTypes";
import { SERVER_URL } from "../utils/consts";
import { getChargeableWeight } from "../utils/helpers";

export const useSendPrompt = () => {
  const [data, setData] = useState<ShippingInfo | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const sendPrompt = async (text: string) => {
    setError(null);

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const controller = new AbortController();
    abortControllerRef.current = controller;

    try {
      setLoading(true);
      const result = await fetch(`${SERVER_URL}/extract`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
        body: JSON.stringify({
          text: text,
        }),
      });

      if (result?.ok) {
        const parsedResult: ShippingInfo = await result.json();
        const proccesedResult: ShippingInfo = {
          ...parsedResult,
          summary: {
            ...parsedResult.summary,
            chargeable_weight: getChargeableWeight(
              parsedResult.summary.volume,
              parsedResult.summary.weight,
            ),
          },
        };

        setData(proccesedResult);
      } else {
        throw new Error(result.statusText);
      }
    } catch {
      setError(
        "An error occurred while processing your request. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return { sendPrompt, data, loading, error };
};
